﻿// <auto-generated />
using System;
using System.Collections.Generic;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CoverGo.PoliciesV3.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CoverGo.PoliciesV3.Domain.Policies.Policy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CancellationReason")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("cancellation_reason");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp")
                        .HasColumnName("end_date");

                    b.Property<Dictionary<string, object>>("Fields")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("fields");

                    b.Property<bool>("IsIssued")
                        .HasColumnType("boolean")
                        .HasColumnName("is_issued");

                    b.Property<bool>("IsPremiumOverriden")
                        .HasColumnType("boolean")
                        .HasColumnName("is_premium_overriden");

                    b.Property<bool>("IsRenewal")
                        .HasColumnType("boolean")
                        .HasColumnName("is_renewal");

                    b.Property<DateTime?>("IssueDate")
                        .HasColumnType("timestamp")
                        .HasColumnName("issue_date");

                    b.Property<string>("OriginalIssuerNumber")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("original_issuer_number");

                    b.Property<string>("PolicyNumber")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("policy_number");

                    b.Property<string>("ProductTreeId")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("product_tree_id");

                    b.Property<string>("RenewalNumber")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("renewal_number");

                    b.Property<int?>("RenewalVersion")
                        .HasColumnType("int")
                        .HasColumnName("renewal_version");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("status");

                    b.HasKey("Id");

                    b.ToTable("policies", (string)null);
                });

            modelBuilder.Entity("CoverGo.PoliciesV3.Domain.PolicyMembers.PolicyMember", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CertificateNumber")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("certificate_number");

                    b.Property<Guid?>("DependentOfPolicyMemberId")
                        .HasColumnType("uuid")
                        .HasColumnName("dependent_of_policy_member_id");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp")
                        .HasColumnName("end_date");

                    b.Property<Dictionary<string, object>>("Fields")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("fields");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("first_name");

                    b.Property<string>("InternalCode")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("internal_code");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_printed");

                    b.Property<bool>("IsRemoved")
                        .HasColumnType("boolean")
                        .HasColumnName("is_removed");

                    b.Property<bool>("IsRenewed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_renewed");

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("last_name");

                    b.Property<string>("PlanId")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("PolicyId")
                        .HasColumnType("uuid")
                        .HasColumnName("policy_id");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp")
                        .HasColumnName("start_date");

                    b.HasKey("Id");

                    b.ToTable("policy_members", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
