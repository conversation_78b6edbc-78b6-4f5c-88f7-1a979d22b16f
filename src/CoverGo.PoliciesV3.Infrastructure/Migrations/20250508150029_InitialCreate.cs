﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CoverGo.PoliciesV3.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "policies",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    policy_number = table.Column<string>(type: "varchar(255)", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp", nullable: true),
                    is_issued = table.Column<bool>(type: "boolean", nullable: false),
                    issue_date = table.Column<DateTime>(type: "timestamp", nullable: true),
                    status = table.Column<string>(type: "varchar(255)", nullable: false),
                    product_tree_id = table.Column<string>(type: "varchar(255)", nullable: false),
                    is_premium_overriden = table.Column<bool>(type: "boolean", nullable: false),
                    cancellation_reason = table.Column<string>(type: "varchar(255)", nullable: true),
                    is_renewal = table.Column<bool>(type: "boolean", nullable: false),
                    renewal_number = table.Column<string>(type: "varchar(255)", nullable: true),
                    renewal_version = table.Column<int>(type: "int", nullable: true),
                    original_issuer_number = table.Column<string>(type: "varchar(255)", nullable: true),
                    fields = table.Column<Dictionary<string, object>>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_policies", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "policy_members",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    internal_code = table.Column<string>(type: "varchar(255)", nullable: false),
                    first_name = table.Column<string>(type: "varchar(255)", nullable: true),
                    last_name = table.Column<string>(type: "varchar(255)", nullable: true),
                    policy_id = table.Column<Guid>(type: "uuid", nullable: false),
                    dependent_of_policy_member_id = table.Column<Guid>(type: "uuid", nullable: true),
                    start_date = table.Column<DateTime>(type: "timestamp", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp", nullable: true),
                    plan_id = table.Column<string>(type: "varchar(255)", nullable: false),
                    is_removed = table.Column<bool>(type: "boolean", nullable: false),
                    is_printed = table.Column<bool>(type: "boolean", nullable: false),
                    certificate_number = table.Column<string>(type: "varchar(255)", nullable: true),
                    is_renewed = table.Column<bool>(type: "boolean", nullable: false),
                    fields = table.Column<Dictionary<string, object>>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_policy_members", x => x.id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "policies");

            migrationBuilder.DropTable(
                name: "policy_members");
        }
    }
}
