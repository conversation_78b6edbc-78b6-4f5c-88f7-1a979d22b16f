using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Policies;

public class PolicyConfiguration : IEntityTypeConfiguration<Policy>
{
    public void Configure(EntityTypeBuilder<Policy> builder)
    {
        var policyStatusConverter = new PolicyConverter.StatusConverter();

        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(Policy)));
        builder.<PERSON><PERSON>ey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Id))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid).ValueGeneratedOnAdd();
        builder.Property(x => x.OriginalPolicyNumber).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.OriginalPolicyNumber))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);
        builder.Property(x => x.StartDate).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.StartDate))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date);
        builder.Property(x => x.EndDate).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.EndDate))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date);
        builder.Property(x => x.IsIssued).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsIssued))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean);
        builder.Property(x => x.IssueDate).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IssueDate))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Timestamp);
        builder.Property(x => x.Status).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Status)));
        builder.Property(x => x.IsPremiumOverriden).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsPremiumOverriden))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean);
        builder.Property(x => x.CancellationReason).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.CancellationReason))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);
        builder.Property(x => x.IsRenewal).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsRenewal))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean);
        builder.Property(x => x.OriginalIssuerNumber).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.OriginalIssuerNumber))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);

        builder.Property(x => x.ProductId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.ProductId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<ProductId>(v, (JsonSerializerOptions?)null));

        builder.Property(x => x.Fields)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Fields)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<PolicyField>>(v, (JsonSerializerOptions?)null) ?? new List<PolicyField>());

        // Navigation properties
        builder.HasMany(x => x.PolicyMembers)
            .WithOne(x => x.Policy)
            .HasForeignKey(x => x.PolicyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Ignore(x => x.EntityAuditInfo);
    }
}