using CoverGo.PoliciesV3.Domain.PolicyMembers;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;

public abstract class PolicyMemberConverter
{
    public class IdConverter() : ValueConverter<PolicyMemberId, Guid>(id => id.Value, value => new PolicyMemberId(value));
    public class NullableIdConverter() : ValueConverter<PolicyMemberId?, Guid?>(x => x != null ? x.Value : null, x => x.HasValue ? new PolicyMemberId(x.Value) : null);
}
