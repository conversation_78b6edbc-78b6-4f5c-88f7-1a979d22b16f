using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;

public class PolicyMemberConfiguration : IEntityTypeConfiguration<PolicyMember>
{
    public void Configure(EntityTypeBuilder<PolicyMember> builder)
    {
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMember)));
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.Id)));
        builder.Property(x => x.PolicyId).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.PolicyId)));
        builder.Property(x => x.MemberId).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.MemberId))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);
        builder.Property(x => x.DependentOfPolicyMemberId).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.DependentOfPolicyMemberId)));
        builder.Property(x => x.StartDate).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.StartDate))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date);
        builder.Property(x => x.EndDate).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.EndDate))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date);
        builder.Property(x => x.PlanId).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.PlanId))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);
        builder.Property(x => x.IsRemoved).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IsRemoved))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean);
        builder.Property(x => x.IsPrinted).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IsPrinted))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean);
        builder.Property(x => x.CertificateNumber).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.CertificateNumber))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);
        builder.Property(x => x.IsRenewed).HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.IsRenewed))).HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean);
        builder.Property(x => x.Fields)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMember.Fields)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<PolicyField>>(v, (JsonSerializerOptions?)null) ?? new List<PolicyField>());
        builder.Ignore(x => x.EntityAuditInfo);
    }
}