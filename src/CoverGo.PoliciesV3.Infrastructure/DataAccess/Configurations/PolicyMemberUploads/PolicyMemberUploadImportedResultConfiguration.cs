using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadImportedResultConfiguration : IEntityTypeConfiguration<PolicyMemberUploadImportedResult>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUploadImportedResult> builder)
    {
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadImportedResult)));
        builder.HasKey(x => x.Id);

        // Primary key
        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        // Foreign key
        builder.Property(x => x.PolicyMemberUploadId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.PolicyMemberUploadId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        // Properties
        builder.Property(x => x.RowIndex)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.RowIndex)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        builder.Property(x => x.Success)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.Success)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .IsRequired();

        builder.Property(x => x.PolicyMemberId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.PolicyMemberId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        builder.Property(x => x.ImportingErrorJson)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.ImportingErrorJson)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .IsRequired(false);

        // Index on foreign key
        builder.HasIndex(x => x.PolicyMemberUploadId)
            .HasDatabaseName($"IX_{PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadImportedResult))}_PolicyMemberUploadId");
    }
}
