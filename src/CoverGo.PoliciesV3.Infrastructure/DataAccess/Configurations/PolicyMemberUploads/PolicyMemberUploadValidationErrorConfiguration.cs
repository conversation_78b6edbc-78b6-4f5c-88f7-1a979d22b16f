using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadValidationErrorConfiguration : IEntityTypeConfiguration<PolicyMemberUploadValidationError>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUploadValidationError> builder)
    {
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadValidationError)));
        builder.HasKey(x => x.Id);

        // Primary key
        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        // Foreign key
        builder.Property(x => x.PolicyMemberUploadId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        // Properties
        builder.Property(x => x.RowIndex)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.RowIndex)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        builder.Property(x => x.Code)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Code)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar100)
            .IsRequired();

        builder.Property(x => x.Message)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Message)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Text)
            .IsRequired();

        // Index on foreign key
        builder.HasIndex(x => x.PolicyMemberUploadId)
            .HasDatabaseName($"IX_{PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadValidationError))}_PolicyMemberUploadId");
    }
}
