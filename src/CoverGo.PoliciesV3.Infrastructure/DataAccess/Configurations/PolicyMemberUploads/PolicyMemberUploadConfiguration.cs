using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadConfiguration : IEntityTypeConfiguration<PolicyMemberUpload>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUpload> builder)
    {
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUpload)));
        builder.HasKey(x => x.Id);

        // Primary key
        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        // Foreign keys
        builder.Property(x => x.PolicyId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.PolicyId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        builder.Property(x => x.EndorsementId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.EndorsementId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        // Basic properties
        builder.Property(x => x.Path)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Path)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Text)
            .IsRequired();

        builder.Property(x => x.MembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.MembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        builder.Property(x => x.ValidMembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.ValidMembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired(false);

        builder.Property(x => x.InvalidMembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.InvalidMembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired(false);

        // Status with default value
        builder.Property(x => x.Status)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Status)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar50)
            .IsRequired()
            .HasDefaultValue(PolicyMemberUploadStatus.REGISTERED);

        // Timestamps
        builder.Property(x => x.CreatedAt)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.CreatedAt)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
            .IsRequired()
            .ValueGeneratedOnAdd();

        builder.Property(x => x.LastModifiedAt)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.LastModifiedAt)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(x => x.PolicyId)
            .HasDatabaseName($"IX_{PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUpload))}_PolicyId");

        builder.HasIndex(x => x.EndorsementId)
            .HasDatabaseName($"IX_{PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUpload))}_EndorsementId");

        // Navigation properties
        builder.HasMany(x => x.ValidationErrors)
            .WithOne(x => x.PolicyMemberUpload)
            .HasForeignKey(x => x.PolicyMemberUploadId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.ImportedResults)
            .WithOne(x => x.PolicyMemberUpload)
            .HasForeignKey(x => x.PolicyMemberUploadId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(x => x.EntityAuditInfo);
    }
}
