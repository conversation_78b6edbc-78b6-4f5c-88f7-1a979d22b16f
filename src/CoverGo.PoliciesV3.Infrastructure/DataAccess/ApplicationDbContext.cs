using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Policies;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : DbContext(options)
{
    public DbSet<Policy> Policies => Set<Policy>();
    public DbSet<PolicyMember> PolicyMembers => Set<PolicyMember>();
    public DbSet<PolicyMemberUpload> PolicyMemberUploads => Set<PolicyMemberUpload>();
    public DbSet<PolicyMemberUploadValidationError> PolicyMemberUploadValidationErrors => Set<PolicyMemberUploadValidationError>();
    public DbSet<PolicyMemberUploadImportedResult> PolicyMemberUploadImportedResults => Set<PolicyMemberUploadImportedResult>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        base.ConfigureConventions(configurationBuilder);
        configurationBuilder.Properties<PolicyId>()
            .HaveConversion<PolicyConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyStatus>()
            .HaveConversion<PolicyConverter.StatusConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);

        configurationBuilder.Properties<PolicyMemberId>()
            .HaveConversion<PolicyMemberConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberId?>()
            .HaveConversion<PolicyMemberConverter.NullableIdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberUploadId>()
            .HaveConversion<PolicyMemberUploadConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberUploadStatus>()
            .HaveConversion<PolicyMemberUploadConverter.StatusConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);
    }
}