using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using Humanizer;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;

/// <summary>
/// PostgreSQL-specific database helpers for naming conventions and schema operations.
/// </summary>
public static class PostgreSqlHelpers
{
    /// <summary>
    /// Converts a class name to PostgreSQL table name convention (snake_case and pluralized).
    /// </summary>
    /// <param name="name">The class name to convert</param>
    /// <returns>PostgreSQL-compatible table name</returns>
    public static string GetTableName(string name) => DatabaseNamingHelpers.ConvertToSnakeCase(name.Pluralize());

    /// <summary>
    /// Converts a property name to PostgreSQL column name convention (snake_case).
    /// </summary>
    /// <param name="name">The property name to convert</param>
    /// <returns>PostgreSQL-compatible column name</returns>
    public static string GetColumnName(string name) => DatabaseNamingHelpers.ConvertToSnakeCase(name);
}
