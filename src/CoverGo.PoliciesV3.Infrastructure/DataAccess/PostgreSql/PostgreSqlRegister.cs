using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Collections.Concurrent;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;

public static class PostgreSqlRegister
{
    // Cache of data sources per tenant to avoid recreating them for every request
    private static readonly ConcurrentDictionary<string, NpgsqlDataSource> _dataSources = new();

    private static NpgsqlDataSource GetDataSourceForTenant(IConfiguration configuration, string tenantId, ILogger? logger = null)
    {
        return _dataSources.GetOrAdd(tenantId, tenant =>
        {
            logger?.LogInformation("Creating new data source for tenant {Tenant}", tenant);
            string connectionStringTemplate = configuration.GetConnectionString(ConfigurationConstants.ConnectionStrings.DefaultConnection)!;
            string connectionString = connectionStringTemplate.Replace(ConfigurationConstants.TemplatePlaceholders.Tenant, tenant);

            var dataSourceBuilder = new NpgsqlDataSourceBuilder(connectionString);
            dataSourceBuilder.EnableDynamicJson();
            return dataSourceBuilder.Build();
        });
    }

    public static IServiceCollection AddDatabase<TDbContext>(this IServiceCollection services)
        where TDbContext : DbContext
    {
        services.AddDbContext<TDbContext>((serviceProvider, options) =>
        {
            TenantId? tenantId = serviceProvider.GetService<TenantId>();
            IConfiguration configuration = serviceProvider.GetRequiredService<IConfiguration>();
            ILogger<TDbContext>? logger = serviceProvider.GetService<ILogger<TDbContext>>();

            string tenantName = tenantId?.Value ?? ConfigurationConstants.TemplatePlaceholders.DefaultTenant;

            // Get or create a data source for this tenant
            NpgsqlDataSource dataSource = GetDataSourceForTenant(configuration, tenantName, logger);

            options.UseNpgsql(dataSource)
                   .EnableDetailedErrors();
        });

        services.AddScoped<PostgreSqlUnitOfWork<TDbContext>>();
        services.AddScoped<IUnitOfWork>(provider => provider.GetRequiredService<PostgreSqlUnitOfWork<TDbContext>>());

        return services;
    }

    public static IServiceCollection AddPostgreSqlRepository<TDbContext, TAggregateRoot, TIdentity>(this IServiceCollection services)
        where TDbContext : DbContext
        where TAggregateRoot : class, IAggregateRoot<TIdentity>
    {
        services.AddScoped<PostgreSqlRepository<TDbContext, TAggregateRoot, TIdentity>>();
        services.AddScoped<IRepository<TAggregateRoot, TIdentity>>(provider => provider.GetRequiredService<PostgreSqlRepository<TDbContext, TAggregateRoot, TIdentity>>());
        services.AddScoped<IPaginatedRepository<TAggregateRoot, TIdentity>>(provider => provider.GetRequiredService<PostgreSqlRepository<TDbContext, TAggregateRoot, TIdentity>>());
        return services;
    }
}
