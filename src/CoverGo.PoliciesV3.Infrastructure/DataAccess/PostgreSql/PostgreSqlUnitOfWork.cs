using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;

internal class PostgreSqlUnitOfWork<TDbContext>(TDbContext dbContext) : IUnitOfWork
    where TDbContext : DbContext
{
    private IDbContextTransaction? _currentTransaction;

    public TDbContext DbContext => dbContext;
    public bool HasActiveTransaction => _currentTransaction != null;

    public async Task OpenTransactionAsync()
    {
        if (_currentTransaction != null) throw new InvalidOperationException("A transaction is already open.");

        _currentTransaction = await dbContext.Database.BeginTransactionAsync();
    }

    public async Task CommitAsync()
    {
        if (_currentTransaction == null) throw new InvalidOperationException("No transaction is open to commit.");

        try
        {
            await dbContext.SaveChangesAsync();
            await _currentTransaction.CommitAsync();
        }
        catch
        {
            await RollbackAsync();
            throw;
        }
        finally
        {
            await DisposeTransactionAsync();
        }
    }

    public async Task RollbackAsync()
    {
        if (_currentTransaction == null) throw new InvalidOperationException("No transaction is open to rollback.");

        await _currentTransaction.RollbackAsync();
        await DisposeTransactionAsync();
    }

    private async Task DisposeTransactionAsync()
    {
        if (_currentTransaction != null)
        {
            await _currentTransaction.DisposeAsync();
            _currentTransaction = null;
        }
    }

    // Don't dispose the DbContext here, let the DI container manage its lifecycle
    // The DbContextPool will handle proper DbContext recycling
    public void Dispose()
    {
        if (_currentTransaction != null)
        {
            _currentTransaction.Dispose();
            _currentTransaction = null;
        }
        // We no longer dispose dbContext here
    }
}