﻿<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess" />
    <PackageReference Include="Humanizer.Core" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CoverGo.PoliciesV3.Application\CoverGo.PoliciesV3.Application.csproj" />
    <ProjectReference Include="..\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
	<PackageReference Include="Microsoft.SourceLink.GitHub" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Migrations/" />
  </ItemGroup>
</Project>
