namespace CoverGo.PoliciesV3.Infrastructure.Common.Constants;

/// <summary>
/// Constants for configuration keys to avoid magic strings.
/// Centralizes all configuration-related string literals.
/// </summary>
public static class ConfigurationConstants
{
    /// <summary>
    /// Connection string configuration keys
    /// </summary>
    public static class ConnectionStrings
    {
        public const string DefaultConnection = "DefaultConnection";
    }

    /// <summary>
    /// Template placeholders used in configuration values
    /// </summary>
    public static class TemplatePlaceholders
    {
        public const string Tenant = "{tenant}";
        public const string DefaultTenant = "default";
    }

    /// <summary>
    /// Configuration section names
    /// </summary>
    public static class Sections
    {
        public const string Tenants = "Tenants";
        public const string ObservabilityConfiguration = "ObservabilityConfiguration";
    }
}
