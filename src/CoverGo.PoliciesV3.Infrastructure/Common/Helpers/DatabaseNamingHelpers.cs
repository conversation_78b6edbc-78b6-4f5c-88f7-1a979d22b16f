using System.Text;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

/// <summary>
/// Common database naming convention helpers that can be used across different database providers.
/// </summary>
public static class DatabaseNamingHelpers
{
    /// <summary>
    /// Converts PascalCase to snake_case for database naming conventions.
    /// </summary>
    /// <param name="input">The PascalCase string to convert</param>
    /// <returns>The snake_case equivalent</returns>
    /// <exception cref="ArgumentNullException">Thrown when input is null or whitespace</exception>
    public static string ConvertToSnakeCase(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            throw new ArgumentNullException(nameof(input));

        var builder = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            char current = input[i];
            if (char.IsUpper(current) && i > 0)
                builder.Append('_');
            builder.Append(char.ToLower(current));
        }

        return builder.ToString();
    }

    /// <summary>
    /// Converts PascalCase to kebab-case for URL-friendly naming conventions.
    /// </summary>
    /// <param name="input">The PascalCase string to convert</param>
    /// <returns>The kebab-case equivalent</returns>
    /// <exception cref="ArgumentNullException">Thrown when input is null or whitespace</exception>
    public static string ConvertToKebabCase(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            throw new ArgumentNullException(nameof(input));

        var builder = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            char current = input[i];
            if (char.IsUpper(current) && i > 0)
                builder.Append('-');
            builder.Append(char.ToLower(current));
        }

        return builder.ToString();
    }
}
