using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.PoliciesV3.Infrastructure;

public static class InfrastructureRegister
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        services.AddDatabase<ApplicationDbContext>();
        services.AddPostgreSqlRepository<ApplicationDbContext, Policy, PolicyId>();
        services.AddPostgreSqlRepository<ApplicationDbContext, PolicyMember, PolicyMemberId>();
        services.AddPostgreSqlRepository<ApplicationDbContext, PolicyMemberUpload, PolicyMemberUploadId>();

        return services;
    }
}