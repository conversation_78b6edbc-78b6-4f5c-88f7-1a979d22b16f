namespace CoverGo.PoliciesV3.Api.GraphQL.Policies.Create;

[GraphQLDescription("Input for product information")]
public class ProductIdInput
{
    [GraphQLDescription("Product plan name")]
    public string Plan { get; set; } = string.Empty;

    [GraphQLDescription("Product type")]
    public string Type { get; set; } = string.Empty;

    [GraphQLDescription("Product version")]
    public string Version { get; set; } = string.Empty;
}
