namespace CoverGo.PoliciesV3.Api.GraphQL.Policies.Create;

[GraphQLDescription("Input for creating a policy")]
public class CreatePolicyInput
{
    [GraphQLDescription("Start date of the policy")]
    public DateOnly? StartDate { get; set; }

    [GraphQLDescription("End date of the policy")]
    public DateOnly? EndDate { get; set; }

    [GraphQLDescription("Product information (Plan, Type, Version)")]
    public ProductIdInput? ProductId { get; set; }

    [GraphQLDescription("Additional fields for the policy")]
    public List<FieldValuePair>? Fields { get; set; }
}