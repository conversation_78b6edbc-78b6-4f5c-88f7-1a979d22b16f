namespace CoverGo.PoliciesV3.Api.GraphQL.Policies.GetPaged;

[GraphQLDescription("Input for retrieving paged policies")]
public class GetPagedPoliciesInput
{
    [GraphQLDescription("Original policy number to filter by")]
    public string? OriginalPolicyNumber { get; set; }

    [GraphQLDescription("Start date range - from")]
    public DateOnly? StartDateFrom { get; set; }

    [GraphQLDescription("Start date range - to")]
    public DateOnly? StartDateTo { get; set; }

    [GraphQLDescription("End date range - from")]
    public DateOnly? EndDateFrom { get; set; }

    [GraphQLDescription("End date range - to")]
    public DateOnly? EndDateTo { get; set; }

    [GraphQLDescription("Page number (1-based)")]
    public int Page { get; set; } = 1;

    [GraphQLDescription("Number of items per page")]
    public int PageSize { get; set; } = 10;

    [GraphQLDescription("Field to sort by")]
    public string SortBy { get; set; } = "Id";

    [GraphQLDescription("Sort direction (ASC or DESC)")]
    public string SortDirection { get; set; } = "ASC";
}