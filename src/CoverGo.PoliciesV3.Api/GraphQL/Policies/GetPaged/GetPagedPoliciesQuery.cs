using CoverGo.PoliciesV3.Application.Features.Policies.GetPaged;

namespace CoverGo.PoliciesV3.Api.GraphQL.Policies.GetPaged;

[ExtendObjectType(typeof(Query))]
public class GetPagedPoliciesQuery
{
    [GraphQLName("policies")]
    [GraphQLDescription("Get policies with pagination and filtering options")]
    public async Task<GetPagedPoliciesResponse> GetPagedPolicies(
        [GraphQLDescription("Query parameters for paged policies")] GetPagedPoliciesInput input,
        [Service] GetPagedPoliciesHandler handler,
        CancellationToken cancellationToken)
    {
        var request = new GetPagedPoliciesRequest
        {
            OriginalPolicyNumber = input.OriginalPolicyNumber,
            StartDateFrom = input.StartDateFrom,
            StartDateTo = input.StartDateTo,
            EndDateFrom = input.EndDateFrom,
            EndDateTo = input.EndDateTo,
            Page = input.Page,
            PageSize = input.PageSize,
            SortBy = input.SortBy,
            SortDirection = input.SortDirection
        };

        return await handler.<PERSON><PERSON>(request, cancellationToken);
    }
}