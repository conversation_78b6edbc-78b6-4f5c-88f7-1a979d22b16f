using CoverGo.PoliciesV3.Api.GraphQL.Policies.Create;
using CoverGo.PoliciesV3.Api.GraphQL.Policies.GetPaged;
using CoverGo.PoliciesV3.Api.GraphQL.PolicyMembers.Create;
using CoverGo.PoliciesV3.Api.GraphQL.PolicyMembers.GetPaged;
using HotChocolate.Execution.Configuration;

namespace CoverGo.PoliciesV3.Api.GraphQL;

public static class GraphQLExtensions
{
    /// <summary>
    /// Registers all GraphQL types in the application
    /// </summary>
    public static IRequestExecutorBuilder AddGraphQLTypes(this IRequestExecutorBuilder builder)
    {
        return builder
            .AddType<Query>() // Register root query class
            .AddType<Mutation>() // Register root mutation class
            .AddTypeExtension<CreatePolicyMutation>()
            .AddTypeExtension<CreatePolicyMemberMutation>()
            .AddTypeExtension<GetPagedPoliciesQuery>()
            .AddTypeExtension<GetPagedPolicyMembersQuery>();
    }

    /// <summary>
    /// Configures the GraphQL server for CoverGo
    /// </summary>
    public static IServiceCollection AddCoverGoGraphQL(this IServiceCollection services)
    {
        services.AddGraphQLServer()
            .AddQueryType<Query>()
            .AddMutationType<Mutation>()
            .AddGraphQLTypes();

        return services;
    }
}