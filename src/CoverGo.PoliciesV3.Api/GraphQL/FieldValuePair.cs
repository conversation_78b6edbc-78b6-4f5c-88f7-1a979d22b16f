using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Api.GraphQL;

[GraphQLDescription("A key-value pair for additional fields")]
public class FieldValuePair
{
    [GraphQLDescription("Field name")]
    public string Key { get; set; } = string.Empty;

    [GraphQLDescription("Field value")]
    public object? Value { get; set; }

    public static Dictionary<string, object> ToDictionary(List<FieldValuePair> fieldValuePairs)
    {
        var dictionary = new Dictionary<string, object>();
        foreach (FieldValuePair pair in fieldValuePairs)
            dictionary[pair.Key] = pair.Value!;
        return dictionary;
    }

    public static List<PolicyField> ToPolicyFields(List<FieldValuePair> fieldValuePairs)
    {
        var policyFields = new List<PolicyField>();
        foreach (FieldValuePair pair in fieldValuePairs)
            policyFields.Add(new PolicyField(pair.Key, pair.Value));
        return policyFields;
    }
}
