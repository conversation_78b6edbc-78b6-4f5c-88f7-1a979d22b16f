using CoverGo.PoliciesV3.Application.Features.PolicyMembers.Create;

namespace CoverGo.PoliciesV3.Api.GraphQL.PolicyMembers.Create;

[ExtendObjectType(typeof(Mutation))]
public class CreatePolicyMemberMutation
{
    [GraphQLDescription("Create a new policy member")]
    public async Task<CreatePolicyMemberResponse> CreatePolicyMember(
        [GraphQLDescription("Policy member creation details")] CreatePolicyMemberInput input,
        [Service] CreatePolicyMemberHandler handler,
        CancellationToken cancellationToken)
    {
        var request = new CreatePolicyMemberRequest
        {
            MemberId = input.MemberId,
            PolicyId = input.PolicyId,
            DependentOfPolicyMemberId = input.DependentOfPolicyMemberId,
            PlanId = input.PlanId,
            StartDate = input.StartDate,
            EndDate = input.EndDate,
            Fields = input.Fields != null ? FieldValuePair.ToPolicyFields(input.Fields) : []
        };

        return await handler.<PERSON><PERSON>(request, cancellationToken);
    }
}
