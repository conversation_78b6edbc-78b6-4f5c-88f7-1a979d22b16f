using CoverGo.PoliciesV3.Api.GraphQL;

namespace CoverGo.PoliciesV3.Api.GraphQL.PolicyMembers.Create;

[GraphQLDescription("Input for creating a policy member")]
public class CreatePolicyMemberInput
{
    [GraphQLDescription("Member ID of the policy member")]
    public string MemberId { get; set; } = string.Empty;

    [GraphQLDescription("ID of the policy")]
    public Guid PolicyId { get; set; }

    [GraphQLDescription("ID of the dependent policy member, if applicable")]
    public Guid? DependentOfPolicyMemberId { get; set; }

    [GraphQLDescription("Start date of the policy member coverage")]
    public DateOnly? StartDate { get; set; }

    [GraphQLDescription("End date of the policy member coverage")]
    public DateOnly? EndDate { get; set; }

    [GraphQLDescription("Plan ID for the policy member")]
    public string PlanId { get; set; } = string.Empty;

    [GraphQLDescription("Additional fields for the policy member")]
    public List<FieldValuePair>? Fields { get; set; }
}