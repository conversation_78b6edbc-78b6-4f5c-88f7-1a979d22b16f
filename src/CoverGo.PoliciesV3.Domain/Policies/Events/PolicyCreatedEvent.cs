using CoverGo.PoliciesV3.Domain.Common;
namespace CoverGo.PoliciesV3.Domain.Policies.Events;

public class PolicyCreatedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public string PolicyNumber { get; init; } = string.Empty;
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public ProductId? ProductId { get; init; }
    public List<PolicyField> Fields { get; init; } = [];
}