using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies;

public record PolicyStatus(string Value) : ValueObject<string>(Value)
{
    public static PolicyStatus Draft => new PolicyStatus("DRAFT");
    public static PolicyStatus Issued => new PolicyStatus("ISSUED");
    public static PolicyStatus Expired => new PolicyStatus("EXPIRED");
    public static PolicyStatus Cancelled => new PolicyStatus("CANCELLED");
}