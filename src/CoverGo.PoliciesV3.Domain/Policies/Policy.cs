using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Policies.Events;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
namespace CoverGo.PoliciesV3.Domain.Policies;

public class Policy : AggregateRootBase<PolicyId>
{
    public string OriginalPolicyNumber { get; private set; } = string.Empty;
    public DateOnly? StartDate { get; private set; }
    public DateOnly? EndDate { get; private set; }
    public bool IsIssued { get; private set; }
    public DateTime? IssueDate { get; private set; }
    public PolicyStatus Status { get; private set; } = PolicyStatus.Draft;
    public bool IsPremiumOverriden { get; private set; }
    public string? CancellationReason { get; private set; }
    public bool IsRenewal { get; private set; }
    public string? OriginalIssuerNumber { get; private set; }
    public List<PolicyField> Fields { get; private set; } = [];

    /// <summary>
    /// Product information stored as JSON (Plan, Type, Version)
    /// </summary>
    public ProductId? ProductId { get; private set; }

    /// <summary>
    /// Navigation property to PolicyMembers
    /// </summary>
    public virtual ICollection<PolicyMember> PolicyMembers { get; set; } = new HashSet<PolicyMember>();

    private Policy(PolicyId id) : base(id)
    {
    }

    public Policy() : this(PolicyId.Empty)
    {

    }

    public static Policy Create(
        string policyNumber,
        DateOnly startDate,
        DateOnly endDate,
        ProductId? productId = null,
        List<PolicyField>? fields = null)
    {
        var policy = new Policy(PolicyId.New);
        var @event = new PolicyCreatedEvent(policy.Id)
        {
            PolicyNumber = policyNumber,
            StartDate = startDate,
            EndDate = endDate,
            ProductId = productId,
            Fields = fields ?? []
        };
        policy.AddDomainEvent(@event);

        // Apply event data directly to the policy
        policy.OriginalPolicyNumber = @event.PolicyNumber;
        policy.StartDate = @event.StartDate;
        policy.EndDate = @event.EndDate;
        policy.ProductId = @event.ProductId;
        policy.Status = PolicyStatus.Draft;
        policy.Fields = @event.Fields;

        return policy;
    }

    public PolicyMember AddPolicyMember(
        string memberId,
        DateOnly? startDate,
        DateOnly? endDate,
        string planId,
        PolicyMemberId? dependentOfPolicyMemberId = null,
        List<PolicyField>? fields = null)
    {
        var policyMember = PolicyMember.Create(
            Id,
            memberId,
            startDate,
            endDate,
            planId,
            dependentOfPolicyMemberId,
            fields);
        PolicyMembers.Add(policyMember);
        return policyMember;
    }

    /// <summary>
    /// Sets the product information
    /// </summary>
    /// <param name="productInfo">Product information</param>
    public void SetProductId(ProductId productInfo)
    {
        ProductId = productInfo;
    }

    /// <summary>
    /// Cancels the policy with a reason
    /// </summary>
    /// <param name="reason">Cancellation reason</param>
    public void Cancel(string reason)
    {
        if (Status == PolicyStatus.Cancelled)
            throw new InvalidOperationException($"Policy is already {nameof(PolicyStatus.Cancelled)}");

        CancellationReason = reason;
        Status = PolicyStatus.Cancelled;
        AddDomainEvent(new PolicyCancelledEvent(Id));
    }
}