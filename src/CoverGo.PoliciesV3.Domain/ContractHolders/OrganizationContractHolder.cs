namespace CoverGo.PoliciesV3.Domain.ContractHolders;

/// <summary>
/// Represents an organization/company contract holder (corresponds to _t: "Company" in MongoDB)
/// </summary>
public class OrganizationContractHolder : ContractHolder
{
    /// <summary>
    /// Company registration number
    /// </summary>
    public string? RegistrationNumber { get; private set; }
    
    /// <summary>
    /// Nature of business
    /// </summary>
    public string? NatureOfBusiness { get; private set; }
    
    /// <summary>
    /// Organization type (e.g., "Customer")
    /// </summary>
    public string? Type { get; private set; }
    
    /// <summary>
    /// Whether the organization is active
    /// </summary>
    public bool IsActive { get; private set; } = true;

    private OrganizationContractHolder(ContractHolderId id) : base(id)
    {
    }

    public OrganizationContractHolder() : this(ContractHolderId.New)
    {
    }

    /// <summary>
    /// Creates a new organization contract holder
    /// </summary>
    /// <param name="name">Organization name</param>
    /// <param name="internalCode">Internal code</param>
    /// <param name="registrationNumber">Registration number</param>
    /// <param name="natureOfBusiness">Nature of business</param>
    /// <param name="type">Organization type</param>
    /// <param name="fieldsJson">Additional fields as JSON</param>
    /// <param name="createdById">ID of user creating this</param>
    /// <returns>A new OrganizationContractHolder instance</returns>
    public static OrganizationContractHolder Create(
        string? name = null,
        string? internalCode = null,
        string? registrationNumber = null,
        string? natureOfBusiness = null,
        string? type = null,
        string? fieldsJson = null,
        string? createdById = null)
    {
        var organization = new OrganizationContractHolder();
        
        // Set properties directly
        organization.Name = name;
        organization.InternalCode = internalCode;
        organization.RegistrationNumber = registrationNumber;
        organization.NatureOfBusiness = natureOfBusiness;
        organization.Type = type;
        organization.FieldsJson = fieldsJson;
        organization.CreatedById = createdById;

        return organization;
    }

    /// <summary>
    /// Updates organization-specific information
    /// </summary>
    /// <param name="registrationNumber">Updated registration number</param>
    /// <param name="natureOfBusiness">Updated nature of business</param>
    /// <param name="type">Updated organization type</param>
    /// <param name="modifiedById">ID of user making the change</param>
    public void UpdateOrganizationInfo(
        string? registrationNumber = null,
        string? natureOfBusiness = null,
        string? type = null,
        string? modifiedById = null)
    {
        RegistrationNumber = registrationNumber;
        NatureOfBusiness = natureOfBusiness;
        Type = type;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }

    /// <summary>
    /// Activates the organization
    /// </summary>
    /// <param name="modifiedById">ID of user making the change</param>
    public void Activate(string? modifiedById = null)
    {
        IsActive = true;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }

    /// <summary>
    /// Deactivates the organization
    /// </summary>
    /// <param name="modifiedById">ID of user making the change</param>
    public void Deactivate(string? modifiedById = null)
    {
        IsActive = false;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }
}
