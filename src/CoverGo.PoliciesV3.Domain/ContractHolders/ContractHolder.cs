using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.ContractHolders;

/// <summary>
/// Base class for contract holders using Table-Per-Hierarchy (TPH) inheritance
/// </summary>
public abstract class ContractHolder : AggregateRootBase<ContractHolderId>
{
    /// <summary>
    /// Name of the contract holder
    /// </summary>
    public string? Name { get; protected set; }

    /// <summary>
    /// Internal code for the contract holder
    /// </summary>
    public string? InternalCode { get; protected set; }

    /// <summary>
    /// Path to photo/avatar
    /// </summary>
    public string? PhotoPath { get; protected set; }

    /// <summary>
    /// Source system or origin
    /// </summary>
    public string? Source { get; protected set; }

    /// <summary>
    /// Dynamic fields stored as JSON (from MongoDB fields object)
    /// </summary>
    public string? FieldsJson { get; protected set; }

    /// <summary>
    /// When the contract holder was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; protected set; }

    /// <summary>
    /// When the contract holder was last modified
    /// </summary>
    public DateTimeOffset? LastModifiedAt { get; protected set; }

    /// <summary>
    /// ID of user who created this contract holder
    /// </summary>
    public string? CreatedById { get; protected set; }

    /// <summary>
    /// ID of user who last modified this contract holder
    /// </summary>
    public string? LastModifiedById { get; protected set; }

    /// <summary>
    /// Contact information stored as JSON
    /// </summary>
    public List<ContactInfo> Contacts { get; private set; } = [];

    /// <summary>
    /// Identity information stored as JSON
    /// </summary>
    public List<IdentityInfo> Identities { get; private set; } = [];

    /// <summary>
    /// Address information stored as JSON
    /// </summary>
    public List<AddressInfo> Addresses { get; private set; } = [];

    /// <summary>
    /// Navigation property to policies where this is the main contract holder
    /// </summary>
    public virtual ICollection<Policy> PoliciesAsMainHolder { get; set; } = new HashSet<Policy>();

    /// <summary>
    /// Navigation property to policies where this is an additional contract holder
    /// </summary>
    public virtual ICollection<Policy> PoliciesAsOtherHolder { get; set; } = new HashSet<Policy>();

    protected ContractHolder(ContractHolderId id) : base(id)
    {
        CreatedAt = DateTimeOffset.UtcNow;
    }

    protected ContractHolder() : this(ContractHolderId.New)
    {
    }

    /// <summary>
    /// Updates the basic information
    /// </summary>
    /// <param name="name">Updated name</param>
    /// <param name="internalCode">Updated internal code</param>
    /// <param name="modifiedById">ID of user making the change</param>
    public virtual void UpdateBasicInfo(string? name, string? internalCode, string? modifiedById = null)
    {
        Name = name;
        InternalCode = internalCode;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }

    /// <summary>
    /// Updates the fields JSON
    /// </summary>
    /// <param name="fieldsJson">Updated fields as JSON</param>
    /// <param name="modifiedById">ID of user making the change</param>
    public virtual void UpdateFields(string? fieldsJson, string? modifiedById = null)
    {
        FieldsJson = fieldsJson;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }

    /// <summary>
    /// Updates the photo path
    /// </summary>
    /// <param name="photoPath">Updated photo path</param>
    /// <param name="modifiedById">ID of user making the change</param>
    public virtual void UpdatePhoto(string? photoPath, string? modifiedById = null)
    {
        PhotoPath = photoPath;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }

    /// <summary>
    /// Adds a contact to this contract holder
    /// </summary>
    /// <param name="type">Contact type</param>
    /// <param name="value">Contact value</param>
    /// <returns>The created contact</returns>
    public ContactInfo AddContact(string type, string value)
    {
        if (string.IsNullOrWhiteSpace(type))
            throw new ArgumentException($"Contact {nameof(type)} cannot be null or empty", nameof(type));
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException($"Contact {nameof(value)} cannot be null or empty", nameof(value));

        var contact = new ContactInfo(type, value);
        Contacts.Add(contact);
        return contact;
    }

    /// <summary>
    /// Adds an identity to this contract holder
    /// </summary>
    /// <param name="type">Identity type</param>
    /// <param name="value">Identity value</param>
    /// <returns>The created identity</returns>
    public IdentityInfo AddIdentity(string type, string value)
    {
        if (string.IsNullOrWhiteSpace(type))
            throw new ArgumentException($"Identity {nameof(type)} cannot be null or empty", nameof(type));
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException($"Identity {nameof(value)} cannot be null or empty", nameof(value));

        var identity = new IdentityInfo(type, value);
        Identities.Add(identity);
        return identity;
    }

    /// <summary>
    /// Adds an address to this contract holder
    /// </summary>
    /// <param name="type">Address type</param>
    /// <param name="addressDetailsJson">Address details as JSON</param>
    /// <returns>The created address</returns>
    public AddressInfo AddAddress(string type, string? addressDetailsJson = null)
    {
        if (string.IsNullOrWhiteSpace(type))
            throw new ArgumentException($"Address {nameof(type)} cannot be null or empty", nameof(type));

        var address = new AddressInfo(type, addressDetailsJson);
        Addresses.Add(address);
        return address;
    }
}
