namespace CoverGo.PoliciesV3.Domain.ContractHolders;

/// <summary>
/// Represents an individual contract holder (corresponds to _t: "Individual" in MongoDB)
/// </summary>
public class IndividualContractHolder : ContractHolder
{
    /// <summary>
    /// English first name
    /// </summary>
    public string? EnglishFirstName { get; private set; }

    /// <summary>
    /// English last name
    /// </summary>
    public string? EnglishLastName { get; private set; }

    /// <summary>
    /// Chinese first name
    /// </summary>
    public string? ChineseFirstName { get; private set; }

    /// <summary>
    /// Chinese last name
    /// </summary>
    public string? ChineseLastName { get; private set; }

    /// <summary>
    /// Gender
    /// </summary>
    public string? Gender { get; private set; }

    /// <summary>
    /// Date of birth
    /// </summary>
    public DateTimeOffset? DateOfBirth { get; private set; }

    /// <summary>
    /// Individual type (e.g., "Customer")
    /// </summary>
    public string? Type { get; private set; }

    /// <summary>
    /// Title (e.g., "Mr.", "Ms.", "Dr.")
    /// </summary>
    public string? Title { get; private set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; private set; }

    private IndividualContractHolder(ContractHolderId id) : base(id)
    {
    }

    public IndividualContractHolder() : this(ContractHolderId.New)
    {
    }

    /// <summary>
    /// Creates a new individual contract holder
    /// </summary>
    /// <param name="name">Full name</param>
    /// <param name="internalCode">Internal code</param>
    /// <param name="englishFirstName">English first name</param>
    /// <param name="englishLastName">English last name</param>
    /// <param name="chineseFirstName">Chinese first name</param>
    /// <param name="chineseLastName">Chinese last name</param>
    /// <param name="gender">Gender</param>
    /// <param name="dateOfBirth">Date of birth</param>
    /// <param name="type">Individual type</param>
    /// <param name="title">Title</param>
    /// <param name="description">Description</param>
    /// <param name="fieldsJson">Additional fields as JSON</param>
    /// <param name="createdById">ID of user creating this</param>
    /// <returns>A new IndividualContractHolder instance</returns>
    public static IndividualContractHolder Create(
        string? name = null,
        string? internalCode = null,
        string? englishFirstName = null,
        string? englishLastName = null,
        string? chineseFirstName = null,
        string? chineseLastName = null,
        string? gender = null,
        DateTimeOffset? dateOfBirth = null,
        string? type = null,
        string? title = null,
        string? description = null,
        string? fieldsJson = null,
        string? createdById = null)
    {
        var individual = new IndividualContractHolder();
        
        // Set properties directly
        individual.Name = name;
        individual.InternalCode = internalCode;
        individual.EnglishFirstName = englishFirstName;
        individual.EnglishLastName = englishLastName;
        individual.ChineseFirstName = chineseFirstName;
        individual.ChineseLastName = chineseLastName;
        individual.Gender = gender;
        individual.DateOfBirth = dateOfBirth;
        individual.Type = type;
        individual.Title = title;
        individual.Description = description;
        individual.FieldsJson = fieldsJson;
        individual.CreatedById = createdById;

        return individual;
    }

    /// <summary>
    /// Updates individual-specific information
    /// </summary>
    /// <param name="englishFirstName">Updated English first name</param>
    /// <param name="englishLastName">Updated English last name</param>
    /// <param name="chineseFirstName">Updated Chinese first name</param>
    /// <param name="chineseLastName">Updated Chinese last name</param>
    /// <param name="gender">Updated gender</param>
    /// <param name="dateOfBirth">Updated date of birth</param>
    /// <param name="type">Updated individual type</param>
    /// <param name="title">Updated title</param>
    /// <param name="description">Updated description</param>
    /// <param name="modifiedById">ID of user making the change</param>
    public void UpdateIndividualInfo(
        string? englishFirstName = null,
        string? englishLastName = null,
        string? chineseFirstName = null,
        string? chineseLastName = null,
        string? gender = null,
        DateTimeOffset? dateOfBirth = null,
        string? type = null,
        string? title = null,
        string? description = null,
        string? modifiedById = null)
    {
        EnglishFirstName = englishFirstName;
        EnglishLastName = englishLastName;
        ChineseFirstName = chineseFirstName;
        ChineseLastName = chineseLastName;
        Gender = gender;
        DateOfBirth = dateOfBirth;
        Type = type;
        Title = title;
        Description = description;
        LastModifiedAt = DateTimeOffset.UtcNow;
        LastModifiedById = modifiedById;
    }
}
