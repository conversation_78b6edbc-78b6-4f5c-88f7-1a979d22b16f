using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when an exclusion is added to a policy member
/// </summary>
public class PolicyMemberExclusionAddedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public PolicyMemberExclusion Exclusion { get; init; } = null!;

    public PolicyMemberExclusionAddedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId, PolicyMemberExclusion exclusion)
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        Exclusion = exclusion;
    }
}
