using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when a loading is added to a policy member
/// </summary>
public class PolicyMemberLoadingAddedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public PolicyMemberLoading Loading { get; init; } = null!;

    public PolicyMemberLoadingAddedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId, PolicyMemberLoading loading)
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        Loading = loading;
    }
}
