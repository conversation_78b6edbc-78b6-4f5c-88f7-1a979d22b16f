using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

public class PolicyMemberCreatedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public string MemberId { get; init; } = string.Empty;
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public string PlanId { get; init; } = string.Empty;
    public PolicyMemberId? DependentOfPolicyMemberId { get; init; } = null;
    public List<PolicyField> Fields { get; init; } = [];
}