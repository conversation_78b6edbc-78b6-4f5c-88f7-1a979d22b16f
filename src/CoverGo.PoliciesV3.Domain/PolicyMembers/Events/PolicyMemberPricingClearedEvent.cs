using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when pricing is cleared for a policy member
/// </summary>
public class PolicyMemberPricingClearedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;

    public PolicyMemberPricingClearedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId) 
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
    }
}
