using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when underwriting status changes for a policy member
/// </summary>
public class PolicyMemberUnderwritingStatusChangedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public UnderwritingStatus Status { get; init; } = UnderwritingStatus.Pending;
    public string? Remark { get; init; }

    public PolicyMemberUnderwritingStatusChangedEvent(
        PolicyId aggregateId, 
        PolicyMemberId policyMemberId, 
        UnderwritingStatus status, 
        string? remark = null) 
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        Status = status;
        Remark = remark;
    }
}
