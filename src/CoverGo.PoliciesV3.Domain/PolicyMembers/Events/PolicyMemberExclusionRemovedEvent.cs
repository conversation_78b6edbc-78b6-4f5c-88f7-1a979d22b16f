using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when an exclusion is removed from a policy member
/// </summary>
public class PolicyMemberExclusionRemovedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public string ExclusionCode { get; init; } = string.Empty;

    public PolicyMemberExclusionRemovedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId, string exclusionCode) 
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        ExclusionCode = exclusionCode;
    }
}
