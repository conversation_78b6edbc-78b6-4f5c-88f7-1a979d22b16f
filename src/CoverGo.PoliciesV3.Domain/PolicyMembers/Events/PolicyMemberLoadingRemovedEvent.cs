using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when a loading is removed from a policy member
/// </summary>
public class PolicyMemberLoadingRemovedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public string LoadingCode { get; init; } = string.Empty;

    public PolicyMemberLoadingRemovedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId, string loadingCode) 
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        LoadingCode = loadingCode;
    }
}
