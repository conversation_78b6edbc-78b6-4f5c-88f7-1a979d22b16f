using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

/// <summary>
/// Domain event raised when pricing is updated for a policy member
/// </summary>
public class PolicyMemberPricingUpdatedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public PolicyMemberId PolicyMemberId { get; init; } = PolicyMemberId.Empty;
    public PolicyMemberPricing Pricing { get; init; } = null!;

    public PolicyMemberPricingUpdatedEvent(PolicyId aggregateId, PolicyMemberId policyMemberId, PolicyMemberPricing pricing)
        : this(aggregateId)
    {
        PolicyMemberId = policyMemberId;
        Pricing = pricing;
    }
}
