using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Events;
using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers;

public class PolicyMember : AggregateRootBase<PolicyMemberId>
{
    public string MemberId { get; private set; } = string.Empty;
    public PolicyId PolicyId { get; private set; } = PolicyId.Empty;

    // Legacy properties for backward compatibility
    public PolicyMemberId? DependentOfPolicyMemberId { get; private set; }
    public DateOnly? StartDate { get; private set; }
    public DateOnly? EndDate { get; private set; }
    public string PlanId { get; private set; } = string.Empty;
    public bool IsRemoved { get; private set; }
    public bool IsPrinted { get; private set; }
    public string? CertificateNumber { get; private set; }
    public bool IsRenewed { get; private set; }
    public List<PolicyField> Fields { get; private set; } = [];

    // Enhanced properties following QuoteMember pattern
    private List<PolicyMemberState> _states = [];
    public IReadOnlyList<PolicyMemberState> States
    {
        get => _states.AsReadOnly();
        private set
        {
            EnsureValidStateDates(value);
            EnsureValidStateDependencies(value);
            _states = [.. value];
            UpdateDependentStates();
            UpdateLegacyProperties();
        }
    }

    private List<PolicyMemberState> _dependentStates = [];
    public IReadOnlyList<PolicyMemberState> DependentStates => _dependentStates.AsReadOnly();

    public PolicyMemberUnderwriting Underwriting { get; private set; } = PolicyMemberUnderwriting.CreatePending();
    public PolicyMemberPricing? Pricing { get; private set; }

    /// <summary>
    /// Navigation property to the parent Policy
    /// </summary>
    public virtual Policy? Policy { get; set; }

    private PolicyMember(PolicyMemberId id) : base(id)
    {
    }

    public PolicyMember() : this(PolicyMemberId.Empty)
    {
    }

    public static PolicyMember Create(
        PolicyId policyId,
        string memberId,
        DateOnly? startDate,
        DateOnly? endDate,
        string planId,
        PolicyMemberId? dependentOfPolicyMemberId = null,
        List<PolicyField>? fields = null)
    {
        var policyMember = new PolicyMember(PolicyMemberId.New);
        var @event = new PolicyMemberCreatedEvent(policyId)
        {
            PolicyMemberId = policyMember.Id,
            MemberId = memberId,
            StartDate = startDate,
            EndDate = endDate,
            PlanId = planId,
            DependentOfPolicyMemberId = dependentOfPolicyMemberId,
            Fields = fields ?? [],
        };

        policyMember.AddDomainEvent(@event);

        // Apply event data directly to the entity
        policyMember.PolicyId = policyId;
        policyMember.MemberId = memberId;
        policyMember.StartDate = startDate;
        policyMember.EndDate = endDate;
        policyMember.PlanId = planId;
        policyMember.DependentOfPolicyMemberId = dependentOfPolicyMemberId;
        policyMember.Fields = fields ?? [];

        // Create initial state if dates and plan are provided
        if (startDate.HasValue && endDate.HasValue && !string.IsNullOrEmpty(planId))
        {
            var dependency = dependentOfPolicyMemberId != null
                ? PolicyMemberDependency.CreateCustom(dependentOfPolicyMemberId, "DEPENDENT")
                : null;

            var initialState = PolicyMemberState.CreatePrimary(
                startDate.Value,
                endDate.Value,
                planId,
                null,
                fields);

            if (dependency != null)
            {
                initialState = PolicyMemberState.CreateDependent(
                    startDate.Value,
                    endDate.Value,
                    planId,
                    dependency,
                    null,
                    fields);
            }

            policyMember._states.Add(initialState);
        }

        return policyMember;
    }

    #region State Management

    /// <summary>
    /// Adds a new state to the policy member
    /// </summary>
    public void AddState(PolicyMemberState newState)
    {
        if (!newState.IsValid)
            throw new InvalidOperationException($"Invalid state: {nameof(PolicyMemberState.EndDate)} must be after or equal to {nameof(PolicyMemberState.StartDate)}");

        var newStates = _states.ToList();
        newStates.Add(newState);

        // Validate and set states (this will trigger validation)
        States = newStates;

        AddDomainEvent(new PolicyMemberStateAddedEvent(PolicyId, Id, newState));
    }

    /// <summary>
    /// Removes a state from the policy member
    /// </summary>
    public void RemoveState(PolicyMemberState stateToRemove)
    {
        var newStates = _states.Where(s => s != stateToRemove).ToList();
        States = newStates;

        AddDomainEvent(new PolicyMemberStateRemovedEvent(PolicyId, Id, stateToRemove));
    }

    /// <summary>
    /// Gets the active state on a specific date
    /// </summary>
    public PolicyMemberState? GetActiveStateOn(DateOnly date)
        => _states.FirstOrDefault(s => s.IsActiveOn(date));

    /// <summary>
    /// Gets all states that are active during a date range
    /// </summary>
    public IEnumerable<PolicyMemberState> GetActiveStatesDuring(DateOnly startDate, DateOnly endDate)
        => _states.Where(s => s.StartDate <= endDate && s.EndDate >= startDate);

    #endregion

    #region Underwriting Management

    /// <summary>
    /// Accepts the underwriting for this policy member
    /// </summary>
    public void AcceptUnderwriting(
        string? remark = null,
        IReadOnlyList<PolicyMemberLoading>? loadings = null,
        IReadOnlyList<PolicyMemberExclusion>? exclusions = null,
        IReadOnlyList<string>? preExistingConditions = null)
    {
        Underwriting = PolicyMemberUnderwriting.CreateAccepted(remark, loadings, exclusions, preExistingConditions);
        AddDomainEvent(new PolicyMemberUnderwritingStatusChangedEvent(PolicyId, Id, UnderwritingStatus.Accepted, remark));
    }

    /// <summary>
    /// Rejects the underwriting for this policy member
    /// </summary>
    public void RejectUnderwriting(string reason)
    {
        Underwriting = PolicyMemberUnderwriting.CreateRejected(reason);
        Pricing = null; // Clear pricing for rejected members
        AddDomainEvent(new PolicyMemberUnderwritingStatusChangedEvent(PolicyId, Id, UnderwritingStatus.Rejected, reason));
    }

    /// <summary>
    /// Refers the underwriting for this policy member
    /// </summary>
    public void ReferUnderwriting(string? remark = null)
    {
        Underwriting = PolicyMemberUnderwriting.CreateReferred(remark);
        AddDomainEvent(new PolicyMemberUnderwritingStatusChangedEvent(PolicyId, Id, UnderwritingStatus.Referred, remark));
    }

    /// <summary>
    /// Adds a loading to the underwriting
    /// </summary>
    public void AddLoading(PolicyMemberLoading loading)
    {
        Underwriting = Underwriting.AddLoading(loading);
        AddDomainEvent(new PolicyMemberLoadingAddedEvent(PolicyId, Id, loading));
    }

    /// <summary>
    /// Removes a loading from the underwriting
    /// </summary>
    public void RemoveLoading(string loadingCode)
    {
        Underwriting = Underwriting.RemoveLoading(loadingCode);
        AddDomainEvent(new PolicyMemberLoadingRemovedEvent(PolicyId, Id, loadingCode));
    }

    /// <summary>
    /// Adds an exclusion to the underwriting
    /// </summary>
    public void AddExclusion(PolicyMemberExclusion exclusion)
    {
        Underwriting = Underwriting.AddExclusion(exclusion);
        AddDomainEvent(new PolicyMemberExclusionAddedEvent(PolicyId, Id, exclusion));
    }

    /// <summary>
    /// Removes an exclusion from the underwriting
    /// </summary>
    public void RemoveExclusion(string exclusionCode)
    {
        Underwriting = Underwriting.RemoveExclusion(exclusionCode);
        AddDomainEvent(new PolicyMemberExclusionRemovedEvent(PolicyId, Id, exclusionCode));
    }

    #endregion

    #region Pricing Management

    /// <summary>
    /// Updates the pricing for this policy member
    /// </summary>
    public void UpdatePricing(PolicyMemberPricing pricing)
    {
        if (!pricing.IsValid)
            throw new InvalidOperationException($"Invalid pricing: All amounts must be non-negative");

        if (Underwriting.Status == UnderwritingStatus.Rejected)
            throw new InvalidOperationException($"Cannot set pricing for {nameof(UnderwritingStatus.Rejected)} members");

        Pricing = pricing;
        AddDomainEvent(new PolicyMemberPricingUpdatedEvent(PolicyId, Id, pricing));
    }

    /// <summary>
    /// Clears the pricing for this policy member
    /// </summary>
    public void ClearPricing()
    {
        Pricing = null;
        AddDomainEvent(new PolicyMemberPricingClearedEvent(PolicyId, Id));
    }

    /// <summary>
    /// Checks if pricing can be calculated for this member
    /// </summary>
    public bool CanRunPricing =>
        Underwriting.Status != UnderwritingStatus.Rejected &&
        _states.Any() &&
        _states.All(s => !string.IsNullOrEmpty(s.PlanId));

    #endregion

    #region Validation Methods

    /// <summary>
    /// Validates that states do not overlap and are in chronological order
    /// </summary>
    private void EnsureValidStateDates(IEnumerable<PolicyMemberState> states)
    {
        var stateList = states.OrderBy(s => s.StartDate).ToList();

        for (int i = 0; i < stateList.Count; i++)
        {
            var currentState = stateList[i];

            if (!currentState.IsValid)
                throw new InvalidOperationException($"Invalid state: {nameof(PolicyMemberState.EndDate)} {currentState.EndDate} must be after or equal to {nameof(PolicyMemberState.StartDate)} {currentState.StartDate}");

            // Check for overlaps with next state
            if (i < stateList.Count - 1)
            {
                var nextState = stateList[i + 1];
                if (currentState.OverlapsWith(nextState))
                    throw new InvalidOperationException($"State overlap detected: State ending {currentState.EndDate} overlaps with state starting {nextState.StartDate}");
            }
        }
    }

    /// <summary>
    /// Validates dependency relationships in states
    /// </summary>
    private void EnsureValidStateDependencies(IEnumerable<PolicyMemberState> states)
    {
        foreach (var state in states)
        {
            if (state.Dependency?.PrimaryMemberId == Id)
                throw new InvalidOperationException($"A member cannot be dependent on themselves");
        }
    }

    /// <summary>
    /// Updates dependent states when this member's states change
    /// </summary>
    private void UpdateDependentStates()
    {
        // This would typically be handled by a domain service or application service
        // that has access to other policy members to update their dependent states
        // For now, we'll just clear the local dependent states list
        _dependentStates.Clear();
    }

    #endregion

    #region Business Logic Properties

    /// <summary>
    /// Checks if this member is currently active
    /// </summary>
    public bool IsActive => _states.Any(s => s.IsActiveOn(DateOnly.FromDateTime(DateTime.UtcNow)));

    /// <summary>
    /// Checks if this member has any dependents
    /// </summary>
    public bool HasDependents => _dependentStates.Any();

    /// <summary>
    /// Checks if this member is a dependent of another member
    /// </summary>
    public bool IsDependent => _states.Any(s => s.IsDependent);

    /// <summary>
    /// Gets the current active state
    /// </summary>
    public PolicyMemberState? CurrentState => GetActiveStateOn(DateOnly.FromDateTime(DateTime.UtcNow));

    /// <summary>
    /// Gets the total premium from pricing
    /// </summary>
    public decimal? TotalPremium => Pricing?.TotalPremium;

    #endregion

    #region Legacy Support Methods

    /// <summary>
    /// Updates legacy properties when states change (for backward compatibility)
    /// </summary>
    private void UpdateLegacyProperties()
    {
        var firstState = _states.OrderBy(s => s.StartDate).FirstOrDefault();
        if (firstState != null)
        {
            StartDate = firstState.StartDate;
            EndDate = firstState.EndDate;
            PlanId = firstState.PlanId;
            DependentOfPolicyMemberId = firstState.Dependency?.PrimaryMemberId;
        }
    }

    #endregion
}