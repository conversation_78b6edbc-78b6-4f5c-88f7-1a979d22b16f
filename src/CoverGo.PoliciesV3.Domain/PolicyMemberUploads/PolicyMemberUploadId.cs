using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Strongly-typed identifier for PolicyMemberUpload entities
/// </summary>
public record PolicyMemberUploadId(Guid Value) : ValueObject<Guid>(Value)
{
    public static PolicyMemberUploadId Empty => new(Guid.Empty);
    public static PolicyMemberUploadId New => new(Guid.CreateVersion7());
}
