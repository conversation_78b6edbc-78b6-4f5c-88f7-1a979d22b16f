using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents the status of a policy member upload process
/// </summary>
public record PolicyMemberUploadStatus(string Value) : ValueObject<string>(Value)
{
    public static PolicyMemberUploadStatus REGISTERED => new("REGISTERED");
    public static PolicyMemberUploadStatus VALIDATING => new("VALIDATING");
    public static PolicyMemberUploadStatus VALIDATION_COMPLETED => new("VALIDATION_COMPLETED");
    public static PolicyMemberUploadStatus VALIDATION_FAILED => new("VALIDATION_FAILED");
    public static PolicyMemberUploadStatus IMPORTING => new("IMPORTING");
    public static PolicyMemberUploadStatus IMPORT_COMPLETED => new("IMPORT_COMPLETED");
    public static PolicyMemberUploadStatus IMPORT_FAILED => new("IMPORT_FAILED");
    public static PolicyMemberUploadStatus FAILED => new("FAILED");
}
