namespace CoverGo.PoliciesV3.Domain.Common;

/// <summary>
/// Represents the underwriting status of a policy member
/// </summary>
public record UnderwritingStatus(string Value) : ValueObject<string>(Value)
{
    public static UnderwritingStatus Pending => new("PENDING");
    public static UnderwritingStatus Accepted => new("ACCEPTED");
    public static UnderwritingStatus Rejected => new("REJECTED");
    public static UnderwritingStatus Referred => new("REFERRED");
    public static UnderwritingStatus RequiresReview => new("REQUIRES_REVIEW");
}
