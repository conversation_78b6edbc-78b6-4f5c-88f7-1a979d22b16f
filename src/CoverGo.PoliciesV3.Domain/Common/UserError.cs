namespace CoverGo.PoliciesV3.Domain.Common;

/// <summary>
/// Simple implementation of IUserError for representing user-facing errors
/// </summary>
public class UserError : IUserError
{
    public string Code { get; }
    public string Message { get; }

    public UserError(string code, string message)
    {
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Message = message ?? throw new ArgumentNullException(nameof(message));
    }

    /// <summary>
    /// Creates a validation error
    /// </summary>
    public static UserError ValidationError(string message) => new("VALIDATION_ERROR", message);

    /// <summary>
    /// Creates an import error
    /// </summary>
    public static UserError ImportError(string message) => new("IMPORT_ERROR", message);

    /// <summary>
    /// Creates a business rule error
    /// </summary>
    public static UserError BusinessRuleError(string message) => new("BUSINESS_RULE_ERROR", message);
}
