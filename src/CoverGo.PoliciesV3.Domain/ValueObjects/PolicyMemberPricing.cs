namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents the pricing information for a policy member
/// </summary>
public record PolicyMemberPricing(
    decimal GrossPremium,
    decimal NetPremium,
    decimal Tax,
    decimal Discount,
    string Currency,
    DateTime CalculatedAt,
    string? CalculationReference = null)
{
    /// <summary>
    /// Creates a new pricing instance
    /// </summary>
    public static PolicyMemberPricing Create(
        decimal grossPremium,
        decimal netPremium,
        decimal tax,
        decimal discount,
        string currency,
        string? calculationReference = null)
        => new(grossPremium, netPremium, tax, discount, currency, DateTime.UtcNow, calculationReference);

    /// <summary>
    /// Creates a zero pricing instance
    /// </summary>
    public static PolicyMemberPricing CreateZero(string currency)
        => new(0, 0, 0, 0, currency, DateTime.UtcNow);

    /// <summary>
    /// Total premium including tax and discount
    /// </summary>
    public decimal TotalPremium => GrossPremium + Tax - Discount;

    /// <summary>
    /// Checks if pricing is valid (non-negative values)
    /// </summary>
    public bool IsValid => GrossPremium >= 0 && NetPremium >= 0 && Tax >= 0 && Discount >= 0;
}
