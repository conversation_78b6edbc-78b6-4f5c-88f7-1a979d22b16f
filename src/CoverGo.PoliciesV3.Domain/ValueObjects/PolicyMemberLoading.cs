namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents an underwriting loading applied to a policy member
/// </summary>
public record PolicyMemberLoading(
    string Code,
    string Description,
    decimal Amount,
    string Type, // "PERCENTAGE" or "FLAT"
    string? Reason = null)
{
    /// <summary>
    /// Creates a percentage-based loading
    /// </summary>
    public static PolicyMemberLoading CreatePercentage(string code, string description, decimal percentage, string? reason = null)
        => new(code, description, percentage, "PERCENTAGE", reason);

    /// <summary>
    /// Creates a flat amount loading
    /// </summary>
    public static PolicyMemberLoading CreateFlat(string code, string description, decimal amount, string? reason = null)
        => new(code, description, amount, "FLAT", reason);
}
