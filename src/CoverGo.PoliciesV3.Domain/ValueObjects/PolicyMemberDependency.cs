using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a dependency relationship between policy members
/// </summary>
public record PolicyMemberDependency(
    PolicyMemberId PrimaryMemberId,
    string RelationshipType,
    string? RelationshipDescription = null)
{
    /// <summary>
    /// Common relationship types
    /// </summary>
    public static class RelationshipTypes
    {
        public const string Spouse = "SPOUSE";
        public const string Child = "CHILD";
        public const string Parent = "PARENT";
        public const string Sibling = "SIBLING";
        public const string Partner = "PARTNER";
        public const string Other = "OTHER";
    }

    /// <summary>
    /// Creates a spouse dependency
    /// </summary>
    public static PolicyMemberDependency CreateSpouse(PolicyMemberId primaryMemberId)
        => new(primaryMemberId, RelationshipTypes.Spouse, "Spouse");

    /// <summary>
    /// Creates a child dependency
    /// </summary>
    public static PolicyMemberDependency CreateChild(PolicyMemberId primaryMemberId)
        => new(primaryMemberId, RelationshipTypes.Child, "Child");

    /// <summary>
    /// Creates a custom dependency
    /// </summary>
    public static PolicyMemberDependency CreateCustom(PolicyMemberId primaryMemberId, string relationshipType, string? description = null)
        => new(primaryMemberId, relationshipType, description);
}
