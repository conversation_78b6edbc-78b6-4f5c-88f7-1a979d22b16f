using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a temporal state of a policy member with specific coverage details
/// </summary>
public record PolicyMemberState(
    DateOnly StartDate,
    DateOnly EndDate,
    string PlanId,
    string? Class = null,
    PolicyMemberDependency? Dependency = null,
    List<PolicyField>? CustomFields = null)
{
    /// <summary>
    /// Custom fields for this state
    /// </summary>
    public List<PolicyField> Fields { get; init; } = CustomFields ?? [];

    /// <summary>
    /// Creates a primary member state (no dependency)
    /// </summary>
    public static PolicyMemberState CreatePrimary(
        DateOnly startDate,
        DateOnly endDate,
        string planId,
        string? @class = null,
        List<PolicyField>? customFields = null)
        => new(startDate, endDate, planId, @class, null, customFields);

    /// <summary>
    /// Creates a dependent member state
    /// </summary>
    public static PolicyMemberState CreateDependent(
        DateOnly startDate,
        DateOnly endDate,
        string planId,
        PolicyMemberDependency dependency,
        string? @class = null,
        List<PolicyField>? customFields = null)
        => new(startDate, endDate, planId, @class, dependency, customFields);

    /// <summary>
    /// Checks if this state overlaps with another state
    /// </summary>
    public bool OverlapsWith(PolicyMemberState other)
        => StartDate <= other.EndDate && EndDate >= other.StartDate;

    /// <summary>
    /// Checks if this state is valid (end date after start date)
    /// </summary>
    public bool IsValid => EndDate >= StartDate;

    /// <summary>
    /// Checks if this is a dependent state
    /// </summary>
    public bool IsDependent => Dependency != null;

    /// <summary>
    /// Checks if this is a primary state
    /// </summary>
    public bool IsPrimary => Dependency == null;

    /// <summary>
    /// Gets the duration of this state in days
    /// </summary>
    public int DurationInDays => EndDate.DayNumber - StartDate.DayNumber + 1;

    /// <summary>
    /// Checks if the state is active on a specific date
    /// </summary>
    public bool IsActiveOn(DateOnly date)
        => date >= StartDate && date <= EndDate;
}
