using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents the underwriting information for a policy member
/// </summary>
public record PolicyMemberUnderwriting(
    UnderwritingStatus Status,
    string? Remark,
    IReadOnlyList<PolicyMemberLoading> Loadings,
    IReadOnlyList<PolicyMemberExclusion> Exclusions,
    IReadOnlyList<string> PreExistingConditions,
    bool NeedsUnderwriting = false)
{
    /// <summary>
    /// Creates a pending underwriting state
    /// </summary>
    public static PolicyMemberUnderwriting CreatePending()
        => new(UnderwritingStatus.Pending, null, [], [], [], true);

    /// <summary>
    /// Creates an accepted underwriting state
    /// </summary>
    public static PolicyMemberUnderwriting CreateAccepted(
        string? remark = null,
        IReadOnlyList<PolicyMemberLoading>? loadings = null,
        IReadOnlyList<PolicyMemberExclusion>? exclusions = null,
        IReadOnlyList<string>? preExistingConditions = null)
        => new(UnderwritingStatus.Accepted, remark, loadings ?? [], exclusions ?? [], preExistingConditions ?? [], false);

    /// <summary>
    /// Creates a rejected underwriting state
    /// </summary>
    public static PolicyMemberUnderwriting CreateRejected(string reason)
        => new(UnderwritingStatus.Rejected, reason, [], [], [], false);

    /// <summary>
    /// Creates a referred underwriting state
    /// </summary>
    public static PolicyMemberUnderwriting CreateReferred(string? remark = null)
        => new(UnderwritingStatus.Referred, remark, [], [], [], true);

    /// <summary>
    /// Adds a loading to the underwriting
    /// </summary>
    public PolicyMemberUnderwriting AddLoading(PolicyMemberLoading loading)
    {
        var newLoadings = Loadings.ToList();
        newLoadings.Add(loading);
        return this with { Loadings = newLoadings.AsReadOnly() };
    }

    /// <summary>
    /// Removes a loading from the underwriting
    /// </summary>
    public PolicyMemberUnderwriting RemoveLoading(string loadingCode)
    {
        var newLoadings = Loadings.Where(l => l.Code != loadingCode).ToList();
        return this with { Loadings = newLoadings.AsReadOnly() };
    }

    /// <summary>
    /// Adds an exclusion to the underwriting
    /// </summary>
    public PolicyMemberUnderwriting AddExclusion(PolicyMemberExclusion exclusion)
    {
        var newExclusions = Exclusions.ToList();
        newExclusions.Add(exclusion);
        return this with { Exclusions = newExclusions.AsReadOnly() };
    }

    /// <summary>
    /// Removes an exclusion from the underwriting
    /// </summary>
    public PolicyMemberUnderwriting RemoveExclusion(string exclusionCode)
    {
        var newExclusions = Exclusions.Where(e => e.Code != exclusionCode).ToList();
        return this with { Exclusions = newExclusions.AsReadOnly() };
    }

    /// <summary>
    /// Adds a pre-existing condition
    /// </summary>
    public PolicyMemberUnderwriting AddPreExistingCondition(string condition)
    {
        var newConditions = PreExistingConditions.ToList();
        newConditions.Add(condition);
        return this with { PreExistingConditions = newConditions.AsReadOnly() };
    }

    /// <summary>
    /// Removes a pre-existing condition
    /// </summary>
    public PolicyMemberUnderwriting RemovePreExistingCondition(string condition)
    {
        var newConditions = PreExistingConditions.Where(c => c != condition).ToList();
        return this with { PreExistingConditions = newConditions.AsReadOnly() };
    }
}
