namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents an underwriting exclusion applied to a policy member
/// </summary>
public record PolicyMemberExclusion(
    string Code,
    string Description,
    string? Reason = null,
    DateOnly? EffectiveDate = null,
    DateOnly? ExpiryDate = null)
{
    /// <summary>
    /// Creates a permanent exclusion
    /// </summary>
    public static PolicyMemberExclusion CreatePermanent(string code, string description, string? reason = null)
        => new(code, description, reason);

    /// <summary>
    /// Creates a temporary exclusion with expiry date
    /// </summary>
    public static PolicyMemberExclusion CreateTemporary(string code, string description, DateOnly expiryDate, string? reason = null)
        => new(code, description, reason, DateOnly.FromDateTime(DateTime.UtcNow), expiryDate);
}
