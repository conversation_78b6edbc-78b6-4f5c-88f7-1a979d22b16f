using FluentValidation;

namespace CoverGo.PoliciesV3.Application.Features.Policies.Create;

public class CreatePolicyValidator : AbstractValidator<CreatePolicyRequest>
{
    public CreatePolicyValidator()
    {
        RuleFor(x => x.StartDate)
            .NotNull().WithMessage($"{nameof(CreatePolicyRequest.StartDate)} is required");

        RuleFor(x => x.EndDate)
            .NotNull().WithMessage($"{nameof(CreatePolicyRequest.EndDate)} is required")
            .GreaterThan(x => x.StartDate).WithMessage($"{nameof(CreatePolicyRequest.EndDate)} must be after {nameof(CreatePolicyRequest.StartDate)}");


    }
}