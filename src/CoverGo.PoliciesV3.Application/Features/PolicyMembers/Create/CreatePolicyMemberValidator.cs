using FluentValidation;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.Create;

public class CreatePolicyMemberValidator : AbstractValidator<CreatePolicyMemberRequest>
{
    public CreatePolicyMemberValidator()
    {
        RuleFor(x => x.MemberId)
            .NotEmpty().WithMessage($"{nameof(CreatePolicyMemberRequest.MemberId)} is required");

        RuleFor(x => x.PolicyId)
            .NotEmpty().WithMessage($"{nameof(CreatePolicyMemberRequest.PolicyId)} is required");

        RuleFor(x => x.EndDate)
            .GreaterThan(x => x.StartDate).WithMessage($"{nameof(CreatePolicyMemberRequest.EndDate)} must be after {nameof(CreatePolicyMemberRequest.StartDate)}")
            .When(x => x.StartDate.HasValue && x.EndDate.HasValue);

        RuleFor(x => x.PlanId)
            .NotEmpty().WithMessage($"{nameof(CreatePolicyMemberRequest.PlanId)} is required");
    }
}