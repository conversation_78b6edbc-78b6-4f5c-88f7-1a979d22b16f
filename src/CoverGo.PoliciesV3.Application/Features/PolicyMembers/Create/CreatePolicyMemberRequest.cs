using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.Create;

public class CreatePolicyMemberRequest
{
    public string MemberId { get; init; } = string.Empty;
    public Guid PolicyId { get; init; }
    public Guid? DependentOfPolicyMemberId { get; init; }
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public string PlanId { get; init; } = string.Empty;
    public List<PolicyField> Fields { get; init; } = [];
}