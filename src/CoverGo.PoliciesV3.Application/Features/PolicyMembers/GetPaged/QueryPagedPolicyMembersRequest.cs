namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.GetPaged;

public class GetPagedPolicyMembersRequest
{
    public Guid PolicyId { get; set; }
    public string? MemberId { get; set; }
    public DateOnly? StartDateFrom { get; set; }
    public DateOnly? StartDateTo { get; set; }
    public DateOnly? EndDateFrom { get; set; }
    public DateOnly? EndDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "Id";
    public string SortDirection { get; set; } = "ASC";
    public int Skip => (Page - 1) * PageSize;
    public int Take => PageSize;
}