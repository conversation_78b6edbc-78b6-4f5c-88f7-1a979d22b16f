using CoverGo.PoliciesV3.Application.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.GetPaged;

public class GetPagedPolicyMembersHandler(
    IPaginatedRepository<PolicyMember, PolicyMemberId> policyMemberRepository)
{
    public async Task<GetPagedPolicyMembersResponse> Handle(GetPagedPolicyMembersRequest request, CancellationToken cancellationToken)
    {
        // Query the repository with pagination
        var pageResult = await policyMemberRepository.GetPagedAsync(
            x => x.PolicyId == new PolicyId(request.PolicyId)
                && (string.IsNullOrWhiteSpace(request.MemberId) || x.MemberId.Contains(request.MemberId))
                && (!request.StartDateFrom.HasValue || x.StartDate >= request.StartDateFrom)
                && (!request.StartDateTo.HasValue || x.StartDate <= request.StartDateTo)
                && (!request.EndDateFrom.HasValue || x.EndDate >= request.EndDateFrom)
                && (!request.EndDateTo.HasValue || x.EndDate <= request.EndDateTo),
            request.Skip,
            request.Take,
            request.SortBy,
            request.SortDirection,
            cancellationToken);

        return new GetPagedPolicyMembersResponse(
            pageResult.Items.Select(x => new PolicyMemberDto
            {
                PolicyMemberId = x.Id.Value,
                PolicyId = x.PolicyId.Value,
                MemberId = x.MemberId,
                StartDate = x.StartDate,
                EndDate = x.EndDate,
            }),
            pageResult.TotalCount,
            request.Page,
            request.PageSize);
    }
}