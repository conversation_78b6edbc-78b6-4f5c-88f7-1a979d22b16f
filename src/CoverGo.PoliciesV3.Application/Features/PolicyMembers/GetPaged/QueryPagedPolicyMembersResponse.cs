using CoverGo.PoliciesV3.Application.Common;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.GetPaged;

public class GetPagedPolicyMembersResponse(IEnumerable<PolicyMemberDto> items, int totalCount, int pageNumber, int pageSize)
    : PageResult<PolicyMemberDto>(items, totalCount, pageNumber, pageSize)
{
}

public class PolicyMemberDto
{
    public Guid PolicyMemberId { get; set; }
    public Guid PolicyId { get; set; }
    public string MemberId { get; set; } = string.Empty;
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
}