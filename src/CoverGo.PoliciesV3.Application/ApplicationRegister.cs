using CoverGo.PoliciesV3.Application.Features.Policies.Create;
using CoverGo.PoliciesV3.Application.Features.Policies.GetPaged;
using CoverGo.PoliciesV3.Application.Features.PolicyMembers.Create;
using CoverGo.PoliciesV3.Application.Features.PolicyMembers.GetPaged;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.PoliciesV3.Application;

public static class ApplicationRegister
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // Register all validators from assembly
        services.AddValidatorsFromAssembly(typeof(ApplicationRegister).Assembly);

        // Register handlers
        services.AddScoped<CreatePolicyHandler>();
        services.AddScoped<CreatePolicyMemberHandler>();
        services.AddScoped<GetPagedPoliciesHandler>();
        services.AddScoped<GetPagedPolicyMembersHandler>();
        return services;
    }
}
