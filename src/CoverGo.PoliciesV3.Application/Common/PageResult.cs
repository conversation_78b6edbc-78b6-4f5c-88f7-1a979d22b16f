namespace CoverGo.PoliciesV3.Application.Common;

/// <summary>
/// Represents a paginated result set with metadata.
/// </summary>
/// <typeparam name="T">The type of items in the result set.</typeparam>
/// <remarks>
/// Initializes a new instance of the <see cref="PageResult{T}"/> class.
/// </remarks>
/// <param name="items">The items for the current page.</param>
/// <param name="totalCount">The total count of items across all pages.</param>
/// <param name="pageNumber">The current page number (1-based).</param>
/// <param name="pageSize">The page size.</param>
public class PageResult<T>(IEnumerable<T> items, int totalCount, int pageNumber, int pageSize)
{
    /// <summary>
    /// Gets the collection of items for the current page.
    /// </summary>
    public IReadOnlyCollection<T> Items { get; } = items?.ToArray() ?? [];

    /// <summary>
    /// Gets the total number of items across all pages.
    /// </summary>
    public int TotalCount { get; } = totalCount;

    /// <summary>
    /// Gets the current page number (1-based).
    /// </summary>
    public int PageNumber { get; } = pageNumber < 1 ? 1 : pageNumber;

    /// <summary>
    /// Gets the size of each page.
    /// </summary>
    public int PageSize { get; } = pageSize < 1 ? 10 : pageSize;

    /// <summary>
    /// Gets the total number of pages.
    /// </summary>
    public int TotalPages => (TotalCount + PageSize - 1) / PageSize;

    /// <summary>
    /// Gets a value indicating whether there is a previous page.
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Gets a value indicating whether there is a next page.
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Creates a new PageResult from raw components typically returned by a repository.
    /// </summary>
    /// <param name="result">Tuple containing items and total count.</param>
    /// <param name="pageNumber">The current page number (1-based).</param>
    /// <param name="pageSize">The page size.</param>
    /// <returns>A new PageResult instance.</returns>
    public static PageResult<T> FromRepository((IEnumerable<T> Items, int TotalCount) result, int pageNumber, int pageSize)
        => new(result.Items, result.TotalCount, pageNumber, pageSize);
}