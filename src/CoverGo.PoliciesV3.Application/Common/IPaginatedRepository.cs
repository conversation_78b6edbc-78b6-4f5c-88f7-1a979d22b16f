using System.Linq.Expressions;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.Entities;

namespace CoverGo.PoliciesV3.Application.Common;
public interface IPaginatedRepository<TAggregateRoot, TIdentity> : IRepository<TAggregateRoot, TIdentity>
    where TAggregateRoot : class, IAggregateRoot<TIdentity>
{
    Task<PageResult<TAggregateRoot>> GetPagedAsync(
        Expression<Func<TAggregateRoot, bool>> filterExpression,
        int skip,
        int take,
        string orderBy = "Id",
        string orderDirection = "ASC",
        CancellationToken cancellationToken = default);
}