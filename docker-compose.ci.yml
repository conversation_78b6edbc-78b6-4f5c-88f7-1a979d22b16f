version: '3.8'

services:
  otel-collector:
    image: otel/opentelemetry-collector
    command: [--config=/etc/otel-collector-config.yaml]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - 4317:4317 # OTLP gRPC receiver
      - 4318:4318 # OTLP http receiver
      - 55679:55679 # zpages extension
    depends_on:
      - zipkin

  # Zipkin
  zipkin:
    image: openzipkin/zipkin:latest
    restart: always
    ports:
      - "9411:9411"                
        
  covergo-policies-v3:
    environment:
      - ObservabilityConfiguration__CollectorUrl=http://otel-collector:4317
    image: ghcr.io/covergo/policies-v3:latest
    ports:
      - "8080:8080"
    build:
      context: .
      dockerfile: ./Dockerfile     
  
  covergo-policies-v3-tests:
    environment:
      - ObservabilityConfiguration__CollectorUrl=http://otel-collector:4317
    image: ghcr.io/covergo/policies-v3-test:latest
    ports:
      - "8080:8080"
    build:
      context: .
      dockerfile: ./Dockerfile
      target: tests
          
                