# Clean Architecture Structure - PolicyMember Enhancement

## Overview

The PolicyMember enhancement has been reorganized to follow Clean Architecture principles with proper separation of concerns and organized code structure.

## Domain Layer Structure

### 📁 `src/CoverGo.PoliciesV3.Domain/`

#### 📁 `Common/` - Shared Domain Components
Contains shared domain components that can be used across multiple aggregates:

- `DomainEvent.cs` - Base domain event class
- `IUserError.cs` - User error interface
- `UserError.cs` - User error implementation
- `ValueObject.cs` - Base value object class
- **`UnderwritingStatus.cs`** ✨ - Shared underwriting status (moved here)

#### 📁 `ValueObjects/` - Domain Value Objects
Contains all value objects that represent domain concepts:

- **`PolicyMemberLoading.cs`** ✨ - Underwriting loading value object
- **`PolicyMemberExclusion.cs`** ✨ - Underwriting exclusion value object
- **`PolicyMemberPricing.cs`** ✨ - Pricing information value object
- **`PolicyMemberDependency.cs`** ✨ - Member dependency relationship
- **`PolicyMemberUnderwriting.cs`** ✨ - Complete underwriting information
- **`PolicyMemberState.cs`** ✨ - Temporal state management

#### 📁 `Policies/` - Policy Aggregate
Contains the Policy aggregate root and related components:

- `Policy.cs` - Policy aggregate root
- `PolicyId.cs` - Policy identifier
- `PolicyField.cs` - Dynamic policy fields
- `PolicyStatus.cs` - Policy status value object
- `ProductId.cs` - Product information value object
- `Events/` - Policy domain events

#### 📁 `PolicyMembers/` - PolicyMember Aggregate
Contains the PolicyMember aggregate root and related components:

- **`PolicyMember.cs`** ✨ - Enhanced PolicyMember aggregate root
- `PolicyMemberId.cs` - PolicyMember identifier
- `Events/` - PolicyMember domain events
  - `PolicyMemberCreatedEvent.cs` - Original creation event
  - **`PolicyMemberStateAddedEvent.cs`** ✨ - State management events
  - **`PolicyMemberStateRemovedEvent.cs`** ✨
  - **`PolicyMemberUnderwritingStatusChangedEvent.cs`** ✨ - Underwriting events
  - **`PolicyMemberLoadingAddedEvent.cs`** ✨ - Loading management events
  - **`PolicyMemberLoadingRemovedEvent.cs`** ✨
  - **`PolicyMemberExclusionAddedEvent.cs`** ✨ - Exclusion management events
  - **`PolicyMemberExclusionRemovedEvent.cs`** ✨
  - **`PolicyMemberPricingUpdatedEvent.cs`** ✨ - Pricing events
  - **`PolicyMemberPricingClearedEvent.cs`** ✨

#### 📁 `ContractHolders/` - ContractHolder Aggregate
Contains the ContractHolder aggregate and related components:

- `ContractHolder.cs` - Base contract holder
- `ContractHolderId.cs` - Contract holder identifier
- `IndividualContractHolder.cs` - Individual contract holder
- `OrganizationContractHolder.cs` - Organization contract holder
- `AddressInfo.cs`, `ContactInfo.cs`, `IdentityInfo.cs` - Supporting value objects

#### 📁 `PolicyMemberUploads/` - PolicyMemberUpload Aggregate
Contains the PolicyMemberUpload aggregate for batch operations:

- `PolicyMemberUpload.cs` - Upload aggregate root
- `PolicyMemberUploadId.cs` - Upload identifier
- `PolicyMemberUploadStatus.cs` - Upload status
- Related upload management classes

## Clean Architecture Benefits

### ✅ **Proper Separation of Concerns**

1. **Common Components** - Shared across aggregates
   - `UnderwritingStatus` can be used by other entities that need underwriting
   - Base classes and interfaces are centralized

2. **Value Objects** - Encapsulated business logic
   - All value objects are in one place for easy discovery
   - Immutable objects with business rules
   - No dependencies on specific aggregates

3. **Aggregate-Specific** - Domain logic stays with aggregates
   - `PolicyMember` entity contains business logic
   - Events are co-located with the aggregate
   - Clear ownership and boundaries

### ✅ **Dependency Management**

```csharp
// Clean imports following the structure
using CoverGo.PoliciesV3.Domain.Common;        // Shared components
using CoverGo.PoliciesV3.Domain.ValueObjects;  // Value objects
using CoverGo.PoliciesV3.Domain.PolicyMembers; // Aggregate-specific
```

### ✅ **Discoverability and Maintainability**

- **Value Objects**: All in `ValueObjects/` folder - easy to find and reuse
- **Common Components**: Shared logic in `Common/` - prevents duplication
- **Domain Events**: Co-located with aggregates - clear ownership
- **Business Logic**: Encapsulated in appropriate layers

## Usage Examples

### Import Structure
```csharp
// For using shared components
using CoverGo.PoliciesV3.Domain.Common;

// For using value objects
using CoverGo.PoliciesV3.Domain.ValueObjects;

// For using specific aggregates
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.Policies;
```

### Value Object Usage
```csharp
// Create underwriting status (from Common)
var status = UnderwritingStatus.Accepted;

// Create pricing (from ValueObjects)
var pricing = PolicyMemberPricing.Create(1000m, 950m, 100m, 50m, "USD");

// Create loading (from ValueObjects)
var loading = PolicyMemberLoading.CreatePercentage("AGE_LOAD", "Age Loading", 15.0m);

// Create state (from ValueObjects)
var state = PolicyMemberState.CreatePrimary(startDate, endDate, "PLAN_001");
```

## Migration Impact

### ✅ **Backward Compatibility Maintained**
- All existing functionality preserved
- Legacy properties still work
- Gradual migration path available

### ✅ **Enhanced Functionality**
- Rich domain model with proper encapsulation
- Event-driven architecture support
- Comprehensive business rule validation

### ✅ **Future Extensibility**
- Easy to add new value objects
- Clear patterns for new aggregates
- Proper separation enables testing

## File Organization Summary

```
Domain/
├── Common/                     # Shared domain components
│   ├── UnderwritingStatus.cs   # ✨ Moved here (shared)
│   └── ...existing files...
├── ValueObjects/               # All value objects
│   ├── PolicyMemberLoading.cs      # ✨ New
│   ├── PolicyMemberExclusion.cs    # ✨ New  
│   ├── PolicyMemberPricing.cs      # ✨ New
│   ├── PolicyMemberDependency.cs   # ✨ New
│   ├── PolicyMemberUnderwriting.cs # ✨ New
│   └── PolicyMemberState.cs        # ✨ New
├── PolicyMembers/              # PolicyMember aggregate
│   ├── PolicyMember.cs         # ✨ Enhanced
│   ├── Events/                 # Domain events
│   │   ├── ...existing...
│   │   └── ...new events...    # ✨ 8 new events
│   └── PolicyMemberId.cs
└── ...other aggregates...
```

This structure follows Clean Architecture principles while maintaining backward compatibility and providing a solid foundation for future enhancements.
