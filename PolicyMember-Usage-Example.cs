using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Examples;

/// <summary>
/// Example demonstrating the enhanced PolicyMember functionality
/// </summary>
public class PolicyMemberUsageExample
{
    public void DemonstrateEnhancedPolicyMember()
    {
        // Create a policy
        var policy = Policy.Create(
            "POL-2024-001",
            new DateOnly(2024, 1, 1),
            new DateOnly(2024, 12, 31));

        // Create primary member (employee)
        var primaryMember = policy.AddPolicyMember(
            memberId: "EMP001",
            startDate: new DateOnly(2024, 1, 1),
            endDate: new DateOnly(2024, 12, 31),
            planId: "HEALTH_PREMIUM");

        // Accept underwriting for primary member
        primaryMember.AcceptUnderwriting("Standard terms - healthy employee");

        // Set pricing for primary member
        var primaryPricing = PolicyMemberPricing.Create(
            grossPremium: 1500.0m,
            netPremium: 1400.0m,
            tax: 150.0m,
            discount: 0.0m,
            currency: "USD");
        primaryMember.UpdatePricing(primaryPricing);

        // Create spouse as dependent
        var spouseMember = policy.AddPolicyMember(
            memberId: "SPO001",
            startDate: new DateOnly(2024, 1, 1),
            endDate: new DateOnly(2024, 12, 31),
            planId: "HEALTH_PREMIUM",
            dependentOfPolicyMemberId: primaryMember.Id);

        // Add age loading for spouse
        var ageLoading = PolicyMemberLoading.CreatePercentage(
            "AGE_LOAD",
            "Age Loading - Over 45",
            20.0m,
            "Spouse age 47");
        spouseMember.AddLoading(ageLoading);

        // Accept spouse underwriting with loading
        spouseMember.AcceptUnderwriting(
            "Accepted with age loading",
            loadings: new[] { ageLoading });

        // Set pricing for spouse (including loading)
        var spousePricing = PolicyMemberPricing.Create(
            grossPremium: 1200.0m,
            netPremium: 1100.0m,
            tax: 120.0m,
            discount: 0.0m,
            currency: "USD");
        spouseMember.UpdatePricing(spousePricing);

        // Create child as dependent
        var childMember = policy.AddPolicyMember(
            memberId: "CHD001",
            startDate: new DateOnly(2024, 1, 1),
            endDate: new DateOnly(2024, 12, 31),
            planId: "HEALTH_BASIC",
            dependentOfPolicyMemberId: primaryMember.Id);

        // Add pre-existing condition exclusion for child
        var exclusion = PolicyMemberExclusion.CreatePermanent(
            "ASTHMA_EXC",
            "Asthma Exclusion",
            "Pre-existing asthma condition");
        childMember.AddExclusion(exclusion);

        // Accept child underwriting with exclusion
        childMember.AcceptUnderwriting(
            "Accepted with asthma exclusion",
            exclusions: new[] { exclusion });

        // Set pricing for child
        var childPricing = PolicyMemberPricing.Create(
            grossPremium: 600.0m,
            netPremium: 550.0m,
            tax: 60.0m,
            discount: 50.0m, // Family discount
            currency: "USD");
        childMember.UpdatePricing(childPricing);

        // Demonstrate state management - add mid-year plan change for primary
        var upgradedState = PolicyMemberState.CreatePrimary(
            startDate: new DateOnly(2024, 7, 1),
            endDate: new DateOnly(2024, 12, 31),
            planId: "HEALTH_PLATINUM");

        primaryMember.AddState(upgradedState);

        // Demonstrate querying capabilities
        DemonstrateQueries(primaryMember, spouseMember, childMember);
    }

    private void DemonstrateQueries(PolicyMember primary, PolicyMember spouse, PolicyMember child)
    {
        // Check current status
        Console.WriteLine($"Primary member is active: {primary.IsActive}");
        Console.WriteLine($"Primary member has dependents: {primary.HasDependents}");
        Console.WriteLine($"Spouse is dependent: {spouse.IsDependent}");

        // Get current states
        var primaryCurrentState = primary.GetActiveStateOn(DateOnly.FromDateTime(DateTime.UtcNow));
        Console.WriteLine($"Primary current plan: {primaryCurrentState?.PlanId}");

        // Get pricing information
        Console.WriteLine($"Primary total premium: {primary.TotalPremium:C}");
        Console.WriteLine($"Spouse total premium: {spouse.TotalPremium:C}");
        Console.WriteLine($"Child total premium: {child.TotalPremium:C}");

        // Check underwriting status
        Console.WriteLine($"Primary underwriting: {primary.Underwriting.Status.Value}");
        Console.WriteLine($"Spouse loadings: {spouse.Underwriting.Loadings.Count}");
        Console.WriteLine($"Child exclusions: {child.Underwriting.Exclusions.Count}");

        // Calculate family total
        var familyTotal = (primary.TotalPremium ?? 0) +
                         (spouse.TotalPremium ?? 0) +
                         (child.TotalPremium ?? 0);
        Console.WriteLine($"Family total premium: {familyTotal:C}");
    }

    public void DemonstrateAdvancedScenarios()
    {
        var policy = Policy.Create("POL-2024-002",
            new DateOnly(2024, 1, 1),
            new DateOnly(2024, 12, 31));

        // Create member with multiple state changes throughout the year
        var member = policy.AddPolicyMember(
            "EMP002",
            new DateOnly(2024, 1, 1),
            new DateOnly(2024, 3, 31),
            "HEALTH_BASIC");

        // Add Q2 state with plan upgrade
        var q2State = PolicyMemberState.CreatePrimary(
            new DateOnly(2024, 4, 1),
            new DateOnly(2024, 6, 30),
            "HEALTH_PREMIUM");
        member.AddState(q2State);

        // Add Q3-Q4 state with different plan
        var h2State = PolicyMemberState.CreatePrimary(
            new DateOnly(2024, 7, 1),
            new DateOnly(2024, 12, 31),
            "HEALTH_PLATINUM");
        member.AddState(h2State);

        // Demonstrate querying states during different periods
        var q1State = member.GetActiveStateOn(new DateOnly(2024, 2, 15));
        var q2ActiveState = member.GetActiveStateOn(new DateOnly(2024, 5, 15));
        var q4State = member.GetActiveStateOn(new DateOnly(2024, 10, 15));

        Console.WriteLine($"Q1 Plan: {q1State?.PlanId}");
        Console.WriteLine($"Q2 Plan: {q2ActiveState?.PlanId}");
        Console.WriteLine($"Q4 Plan: {q4State?.PlanId}");

        // Get all states active during a specific period
        var midYearStates = member.GetActiveStatesDuring(
            new DateOnly(2024, 3, 15),
            new DateOnly(2024, 7, 15));

        Console.WriteLine($"States active during mid-year period: {midYearStates.Count()}");
    }
}
