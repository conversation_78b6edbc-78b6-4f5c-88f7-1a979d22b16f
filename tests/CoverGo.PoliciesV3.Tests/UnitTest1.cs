using CoverGo.PoliciesV3.Domain.ContractHolders;
using Xunit;

namespace CoverGo.PoliciesV3.Tests;

public class UnitTest1
{
    [Fact]
    [Trait("Category", "Unit")]
    public void Test1()
    {

    }

    [Fact]
    [Trait("Category", "Unit")]
    public void ContractHolder_AddContact_ShouldAddContactInfoToJsonList()
    {
        // Arrange
        var contractHolder = new IndividualContractHolder();

        // Act
        var contact = contractHolder.AddContact("email", "<EMAIL>");

        // Assert
        Assert.NotNull(contact);
        Assert.Equal("email", contact.Type);
        Assert.Equal("<EMAIL>", contact.Value);
        Assert.Single(contractHolder.Contacts);
        Assert.Contains(contact, contractHolder.Contacts);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public void ContractHolder_AddIdentity_ShouldAddIdentityInfoToJsonList()
    {
        // Arrange
        var contractHolder = new IndividualContractHolder();

        // Act
        var identity = contractHolder.AddIdentity("passport", "A12345678");

        // Assert
        Assert.NotNull(identity);
        Assert.Equal("passport", identity.Type);
        Assert.Equal("A12345678", identity.Value);
        Assert.Single(contractHolder.Identities);
        Assert.Contains(identity, contractHolder.Identities);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public void ContractHolder_AddAddress_ShouldAddAddressInfoToJsonList()
    {
        // Arrange
        var contractHolder = new IndividualContractHolder();
        var addressJson = """{"street": "123 Main St", "city": "New York", "country": "USA"}""";

        // Act
        var address = contractHolder.AddAddress("home", addressJson);

        // Assert
        Assert.NotNull(address);
        Assert.Equal("home", address.Type);
        Assert.Equal(addressJson, address.AddressDetailsJson);
        Assert.Single(contractHolder.Addresses);
        Assert.Contains(address, contractHolder.Addresses);
    }
}