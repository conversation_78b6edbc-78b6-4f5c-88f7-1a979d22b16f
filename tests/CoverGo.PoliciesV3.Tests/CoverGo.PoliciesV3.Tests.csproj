﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.collector" />
		<PackageReference Include="JunitXml.TestLogger" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="xunit" />
		<PackageReference Include="xunit.runner.visualstudio" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
	</ItemGroup>

</Project>
