# CoverGo Policies V3 - Project Documentation

## Overview

CoverGo Policies V3 is a microservice built using Clean Architecture principles and Domain-Driven Design (DDD). It manages insurance policies and policy members with a GraphQL API interface, PostgreSQL database, and multi-tenant support.

## Architecture

### Clean Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                        API Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   GraphQL API   │  │   Controllers   │  │   Program   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │    Handlers     │  │   Validators    │  │     DTOs    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │    Entities     │  │  Value Objects  │  │   Events    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Repositories  │  │   DbContext     │  │  Migrations │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Project Structure

```
CoverGo.PoliciesV3/
├── src/
│   ├── CoverGo.PoliciesV3.Api/           # API Layer
│   │   ├── GraphQL/                      # GraphQL schema and resolvers
│   │   │   ├── Policies/                 # Policy-related GraphQL operations
│   │   │   ├── PolicyMembers/            # PolicyMember-related GraphQL operations
│   │   │   ├── Query.cs                  # Root query type
│   │   │   ├── Mutation.cs               # Root mutation type
│   │   │   └── GraphQLExtensions.cs      # GraphQL configuration
│   │   ├── Program.cs                    # Application entry point
│   │   └── appsettings.json              # Configuration
│   │
│   ├── CoverGo.PoliciesV3.Application/   # Application Layer
│   │   ├── Features/                     # Feature-based organization
│   │   │   ├── Policies/                 # Policy use cases
│   │   │   │   ├── Create/               # Create policy feature
│   │   │   │   └── GetPaged/             # Get paged policies feature
│   │   │   └── PolicyMembers/            # PolicyMember use cases
│   │   │       ├── Create/               # Create policy member feature
│   │   │       └── GetPaged/             # Get paged policy members feature
│   │   ├── Common/                       # Shared application components
│   │   └── ApplicationRegister.cs        # DI registration
│   │
│   ├── CoverGo.PoliciesV3.Domain/        # Domain Layer
│   │   ├── Policies/                     # Policy aggregate
│   │   │   ├── Policy.cs                 # Policy entity (Aggregate Root)
│   │   │   ├── PolicyId.cs               # Policy identifier value object
│   │   │   ├── PolicyStatus.cs           # Policy status value object
│   │   │   └── Events/                   # Domain events
│   │   ├── PolicyMembers/                # PolicyMember aggregate
│   │   │   ├── PolicyMember.cs           # PolicyMember entity
│   │   │   ├── PolicyMemberId.cs         # PolicyMember identifier
│   │   │   └── Events/                   # Domain events
│   │   └── Common/                       # Shared domain components
│   │
│   └── CoverGo.PoliciesV3.Infrastructure/ # Infrastructure Layer
│       ├── DataAccess/                   # Data access implementation
│       │   ├── ApplicationDbContext.cs   # EF Core DbContext
│       │   ├── Configurations/           # Entity configurations
│       │   └── PostgreSql/               # PostgreSQL-specific implementations
│       ├── Migrations/                   # Database migrations
│       └── InfrastructureRegister.cs     # DI registration
│
├── tests/
│   └── CoverGo.PoliciesV3.Tests/         # Test project
│
├── helm/                                 # Kubernetes Helm charts
├── docker-compose.yml                    # Docker composition
├── Dockerfile                            # Container definition
└── CoverGo.PoliciesV3.sln              # Solution file
```

## Domain Entities

### Policy (Aggregate Root)

The main aggregate representing an insurance policy.

**Properties:**
- `PolicyId Id` - Unique identifier (GUID)
- `string PolicyNumber` - Auto-generated policy number
- `DateTime? StartDate` - Policy start date
- `DateTime? EndDate` - Policy end date
- `bool IsIssued` - Whether the policy has been issued
- `DateTime? IssueDate` - Date when policy was issued
- `PolicyStatus Status` - Current policy status (Draft, Issued, Expired)
- `string ProductTreeId` - Reference to product configuration
- `bool IsPremiumOverriden` - Whether premium has been manually overridden
- `string? CancellationReason` - Reason for cancellation (if applicable)
- `bool IsRenewal` - Whether this is a renewal policy
- `string? RenewalNumber` - Renewal identifier
- `int? RenewalVersion` - Renewal version number
- `string? OriginalIssuerNumber` - Original issuer reference
- `Dictionary<string, object> Fields` - Dynamic fields for extensibility
- `List<PolicyMember> PolicyMembers` - Collection of policy members

**Key Methods:**
- `static Policy Create(...)` - Factory method for creating new policies
- `PolicyMember AddPolicyMember(...)` - Adds a new member to the policy

### PolicyMember (Aggregate Root)

Represents an individual member covered under a policy.

**Properties:**
- `PolicyMemberId Id` - Unique identifier (GUID)
- `string InternalCode` - Internal member code
- `string? FirstName` - Member's first name
- `string? LastName` - Member's last name
- `PolicyId PolicyId` - Reference to parent policy
- `PolicyMemberId? DependentOfPolicyMemberId` - Reference to primary member (for dependents)
- `DateTime? StartDate` - Coverage start date
- `DateTime? EndDate` - Coverage end date
- `string PlanId` - Reference to coverage plan
- `bool IsRemoved` - Whether member has been removed
- `bool IsPrinted` - Whether certificate has been printed
- `string? CertificateNumber` - Certificate number
- `bool IsRenewed` - Whether member was renewed
- `Dictionary<string, object> Fields` - Dynamic fields for extensibility

**Key Methods:**
- `static PolicyMember Create(...)` - Factory method for creating new policy members

### Value Objects

- **PolicyId**: Strongly-typed identifier for policies
- **PolicyMemberId**: Strongly-typed identifier for policy members
- **PolicyStatus**: Enumeration of policy statuses (Draft, Issued, Expired)

## Application Features

### CQRS Pattern Implementation

The application follows Command Query Responsibility Segregation (CQRS) pattern:

#### Commands (Mutations)
- **CreatePolicy**: Creates a new insurance policy
- **CreatePolicyMember**: Adds a new member to an existing policy

#### Queries
- **GetPagedPolicies**: Retrieves policies with pagination and filtering
- **GetPagedPolicyMembers**: Retrieves policy members with pagination and filtering

### Feature Structure

Each feature follows a consistent structure:
```
Feature/
├── FeatureRequest.cs      # Input model
├── FeatureResponse.cs     # Output model
├── FeatureHandler.cs      # Business logic handler
└── FeatureValidator.cs    # Input validation rules
```

### Validation

Uses FluentValidation for input validation:
- **CreatePolicyValidator**: Validates policy creation requests
- **CreatePolicyMemberValidator**: Validates policy member creation requests

## Infrastructure

### Database

- **Provider**: PostgreSQL with Entity Framework Core
- **Multi-tenancy**: Supports multiple tenants with tenant-specific databases
- **Migrations**: Automatic database schema management

### Repository Pattern

- **IRepository<TAggregateRoot, TIdentity>**: Base repository interface
- **IPaginatedRepository<TAggregateRoot, TIdentity>**: Extended interface for pagination
- **PostgreSqlRepository**: PostgreSQL-specific implementation

### Data Access Features

- Unit of Work pattern
- Automatic transaction management
- Pagination support with filtering and sorting
- Bulk operations (insert, update, delete)

## API Layer

### GraphQL API

The service exposes a GraphQL API with the following capabilities:

#### Queries
```graphql
type Query {
  policies(input: GetPagedPoliciesInput!): GetPagedPoliciesResponse!
  policyMembers(input: GetPagedPolicyMembersInput!): GetPagedPolicyMembersResponse!
}
```

#### Mutations
```graphql
type Mutation {
  createPolicy(input: CreatePolicyInput!): CreatePolicyResponse!
  createPolicyMember(input: CreatePolicyMemberInput!): CreatePolicyMemberResponse!
}
```

### Key Features

- **Hot Chocolate**: GraphQL server implementation
- **Type Extensions**: Modular GraphQL schema organization
- **Input Validation**: Automatic validation using FluentValidation
- **Error Handling**: Structured error responses
- **Documentation**: Built-in GraphQL schema documentation

## Technology Stack

### Core Technologies
- **.NET 8**: Runtime and framework
- **C#**: Programming language
- **PostgreSQL**: Primary database
- **Entity Framework Core**: ORM
- **Hot Chocolate**: GraphQL server

### Libraries and Packages
- **FluentValidation**: Input validation
- **CoverGo Building Blocks**: Internal shared libraries
- **Npgsql**: PostgreSQL driver
- **Microsoft.EntityFrameworkCore**: Data access
- **HotChocolate**: GraphQL implementation

### Development Tools
- **Docker**: Containerization
- **Docker Compose**: Local development environment
- **Helm**: Kubernetes deployment
- **GitVersion**: Semantic versioning
- **OpenTelemetry**: Observability and tracing

## Configuration

### Multi-tenancy
The service supports multi-tenant architecture where each tenant has its own database instance. Tenant resolution is handled through the CoverGo building blocks.

### Environment Configuration
- **Development**: Local development with Docker Compose
- **Production**: Kubernetes deployment with Helm charts

### Key Configuration Sections
- **ConnectionStrings**: Database connection templates
- **Tenants**: List of supported tenants
- **Observability**: Tracing and metrics configuration
- **Authentication**: JWT and authorization settings

## Development Workflow

### Local Development
1. Start dependencies: `docker-compose up`
2. Run migrations: Access `/migration` endpoint
3. Start the API: `dotnet run`
4. Access GraphQL playground: `http://localhost:5000/graphql`

### Testing
- Unit tests in `CoverGo.PoliciesV3.Tests`
- Integration tests with test database
- GraphQL schema testing

### Deployment
- Containerized deployment using Docker
- Kubernetes deployment with Helm charts
- CI/CD pipeline with GitHub Actions

## Domain Events

The system uses domain events for decoupled communication:

- **PolicyCreatedEvent**: Raised when a new policy is created
- **PolicyIssuedEvent**: Raised when a policy is issued
- **PolicyMemberCreatedEvent**: Raised when a new policy member is added

Events follow the pattern:
```csharp
public class DomainEvent<TId> : IDomainEvent
{
    public TId AggregateId { get; }
    public DateTimeOffset OccurredAt { get; }
}
```

## API Examples

### Creating a Policy

**GraphQL Mutation:**
```graphql
mutation CreatePolicy($input: CreatePolicyInput!) {
  createPolicy(input: $input) {
    policyId
    policyNumber
  }
}
```

**Variables:**
```json
{
  "input": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-12-31T23:59:59Z",
    "productTreeId": "health-insurance-v1",
    "fields": [
      {
        "key": "customerName",
        "value": "John Doe"
      },
      {
        "key": "premium",
        "value": 1200.00
      }
    ]
  }
}
```

### Adding a Policy Member

**GraphQL Mutation:**
```graphql
mutation CreatePolicyMember($input: CreatePolicyMemberInput!) {
  createPolicyMember(input: $input) {
    policyMemberId
    internalCode
    policyId
  }
}
```

**Variables:**
```json
{
  "input": {
    "internalCode": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "policyId": "123e4567-e89b-12d3-a456-426614174000",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-12-31T23:59:59Z",
    "planId": "basic-health-plan",
    "fields": [
      {
        "key": "employeeId",
        "value": "EMP001"
      },
      {
        "key": "department",
        "value": "Engineering"
      }
    ]
  }
}
```

### Querying Policies

**GraphQL Query:**
```graphql
query GetPolicies($input: GetPagedPoliciesInput!) {
  policies(input: $input) {
    items {
      policyId
      policyNumber
      startDate
      endDate
      status
      productTreeId
      isIssued
    }
    totalCount
    pageNumber
    pageSize
    totalPages
    hasNextPage
    hasPreviousPage
  }
}
```

**Variables:**
```json
{
  "input": {
    "page": 1,
    "pageSize": 10,
    "sortBy": "PolicyNumber",
    "sortDirection": "ASC",
    "startDateFrom": "2024-01-01T00:00:00Z",
    "startDateTo": "2024-12-31T23:59:59Z"
  }
}
```

## Database Schema

### Policies Table
```sql
CREATE TABLE policies (
    id UUID PRIMARY KEY,
    policy_number VARCHAR(255) NOT NULL,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    is_issued BOOLEAN NOT NULL DEFAULT FALSE,
    issue_date TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    product_tree_id VARCHAR(255) NOT NULL,
    is_premium_overriden BOOLEAN NOT NULL DEFAULT FALSE,
    cancellation_reason VARCHAR(255),
    is_renewal BOOLEAN NOT NULL DEFAULT FALSE,
    renewal_number VARCHAR(255),
    renewal_version INTEGER,
    original_issuer_number VARCHAR(255),
    fields JSONB NOT NULL DEFAULT '{}'
);
```

### Policy Members Table
```sql
CREATE TABLE policy_members (
    id UUID PRIMARY KEY,
    internal_code VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    policy_id UUID NOT NULL,
    dependent_of_policy_member_id UUID,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    plan_id VARCHAR(255) NOT NULL,
    is_removed BOOLEAN NOT NULL DEFAULT FALSE,
    is_printed BOOLEAN NOT NULL DEFAULT FALSE,
    certificate_number VARCHAR(255),
    is_renewed BOOLEAN NOT NULL DEFAULT FALSE,
    fields JSONB NOT NULL DEFAULT '{}'
);
```

## Security

### Authentication
- JWT-based authentication using CoverGo building blocks
- Multi-tenant context resolution
- Role-based authorization

### Data Protection
- Tenant isolation at database level
- Encrypted connections to database
- Audit logging through domain events

## Monitoring and Observability

### Logging
- Structured logging with Serilog
- Correlation IDs for request tracing
- Error logging with stack traces

### Metrics
- Application performance metrics
- Database query performance
- GraphQL operation metrics

### Tracing
- OpenTelemetry integration
- Distributed tracing across services
- Request/response tracing

### Health Checks
- Database connectivity checks
- Dependency health monitoring
- Kubernetes readiness/liveness probes

## Deployment

### Docker
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj", "src/CoverGo.PoliciesV3.Api/"]
# ... additional COPY commands for dependencies
RUN dotnet restore "src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj"
COPY . .
WORKDIR "/src/src/CoverGo.PoliciesV3.Api"
RUN dotnet build "CoverGo.PoliciesV3.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CoverGo.PoliciesV3.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CoverGo.PoliciesV3.Api.dll"]
```

### Kubernetes (Helm)
The service includes Helm charts for Kubernetes deployment with:
- Deployment configurations
- Service definitions
- ConfigMaps for configuration
- Secrets for sensitive data
- Ingress for external access

## Best Practices

### Domain Design
- Aggregate boundaries clearly defined
- Domain events for cross-aggregate communication
- Value objects for type safety
- Factory methods for entity creation

### Application Layer
- CQRS pattern for clear separation of concerns
- Feature-based organization
- Input validation with FluentValidation
- Dependency injection for testability

### Infrastructure
- Repository pattern for data access abstraction
- Unit of Work for transaction management
- Configuration-based multi-tenancy
- Database migrations for schema evolution

### API Design
- GraphQL schema-first approach
- Consistent naming conventions
- Comprehensive input validation
- Structured error responses
- Pagination for large datasets

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify connection string format
   - Check tenant configuration
   - Ensure PostgreSQL is running

2. **Migration Failures**
   - Check database permissions
   - Verify tenant list configuration
   - Review migration scripts

3. **GraphQL Schema Issues**
   - Validate input types
   - Check type extensions registration
   - Review resolver implementations

### Debugging

1. **Enable Detailed Logging**
   ```json
   {
     "Logging": {
       "LogLevel": {
         "Default": "Debug",
         "Microsoft.EntityFrameworkCore": "Information"
       }
     }
   }
   ```

2. **Database Query Logging**
   ```csharp
   options.UseNpgsql(connectionString)
          .EnableDetailedErrors()
          .EnableSensitiveDataLogging(); // Only in development
   ```

3. **GraphQL Debugging**
   - Use GraphQL playground for query testing
   - Enable GraphQL introspection in development
   - Review GraphQL execution logs

## Contributing

### Code Standards
- Follow Clean Architecture principles
- Use Domain-Driven Design patterns
- Implement comprehensive unit tests
- Follow C# coding conventions
- Document public APIs

### Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation if needed
4. Submit pull request with description
5. Address code review feedback
6. Merge after approval

### Testing Guidelines
- Unit tests for domain logic
- Integration tests for repositories
- GraphQL schema tests
- End-to-end API tests

## References

- [CoverGo Application Architecture](https://covergo.atlassian.net/wiki/spaces/BE/pages/1141145643/Application+Architecture)
- [Building Blocks Documentation](https://covergo.atlassian.net/wiki/spaces/BE/pages/1141145690/Building+Blocks+BB)
- [API Guidelines](https://covergo.atlassian.net/wiki/spaces/Engineering/pages/1171521775/API+Guidelines)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)
- [GraphQL Best Practices](https://graphql.org/learn/best-practices/)
