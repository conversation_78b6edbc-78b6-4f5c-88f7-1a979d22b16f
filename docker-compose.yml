version: '3.8'

services:
  covergo-mongo:
    image: mongo:4.0.13
    restart: always
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=local_dev

    ports:
      - "27017:27017"
        
  otel-collector:
    image: otel/opentelemetry-collector
    command: [--config=/etc/otel-collector-config.yaml]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - 4317:4317 # OTLP gRPC receiver
      - 4318:4318 # OTLP http receiver
      - 55679:55679 # zpages extension
    depends_on:
      - zipkin

  # Zipkin
  zipkin:
    image: openzipkin/zipkin:latest
    restart: always
    ports:
      - "9411:9411"        
        
        
  sample-service-graphql:
    environment:
      - ObservabilityConfiguration__CollectorUrl=http://otel-collector:4317
      - MongoDatabaseConfiguration__ConnectionString=********************************************
    image: ${REGISTRY:-covergo}/graphl-sample-service:${TAG:-latest}
    ports:
      - "5201:80"
    build:
      context: .
      dockerfile: src/CoverGo.SampleService.Api/Dockerfile      
          
                