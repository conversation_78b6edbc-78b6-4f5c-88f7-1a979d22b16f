﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine AS base
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS sdk-base
RUN apk add findutils && apk cache clean
WORKDIR /app

FROM sdk-base AS restore
ARG GH_ACCOUNT
ARG GH_TOKEN
COPY nuget.config .
COPY Directory.Packages.props .
COPY Directory.Build.props .
COPY Directory.Build.targets .
COPY ["src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj", "src/CoverGo.PoliciesV3.Api/"]
COPY ["src/CoverGo.PoliciesV3.Application/CoverGo.PoliciesV3.Application.csproj", "src/CoverGo.PoliciesV3.Application/"]
COPY ["src/CoverGo.PoliciesV3.Domain/CoverGo.PoliciesV3.Domain.csproj", "src/CoverGo.PoliciesV3.Domain/"]
COPY ["src/CoverGo.PoliciesV3.Infrastructure/CoverGo.PoliciesV3.Infrastructure.csproj", "src/CoverGo.PoliciesV3.Infrastructure/"]
COPY ["tests/CoverGo.PoliciesV3.Tests/CoverGo.PoliciesV3.Tests.csproj", "tests/CoverGo.PoliciesV3.Tests/"]

RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN find "./" -type f -name "*.csproj" | xargs -L 1 -d '\n' dotnet restore

FROM restore AS publish
ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG APP_VERSION=1.0.0
COPY . .
RUN dotnet build src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj -c "$BUILDCONFIG" --no-restore
RUN dotnet publish src/CoverGo.PoliciesV3.Api/CoverGo.PoliciesV3.Api.csproj -c "$BUILDCONFIG" -o ./publish /p:Version=$APP_VERSION --no-build

FROM base AS runtime
COPY --from=publish /app/publish ./
ENTRYPOINT ["dotnet", "CoverGo.PoliciesV3.Api.dll"]

FROM publish AS tests
ARG BUILDCONFIG=Release
ENV TESTBUILDCONFIG=$BUILDCONFIG
# Change to Env variable when build-publish will support it
ARG FILTER=Unit
ENV TEST_FILTER=$FILTER
RUN dotnet build tests/CoverGo.PoliciesV3.Tests/CoverGo.PoliciesV3.Tests.csproj -c "$BUILDCONFIG" --no-restore
ENTRYPOINT dotnet test --collect:"XPlat Code Coverage" -c "$TESTBUILDCONFIG" --no-build --verbosity normal --settings coverlet.runsettings --filter "Category=$TEST_FILTER"\
  --logger:"junit;LogFileName=TestResults.{assembly}.{framework}.xml;verbosity=normal"\
  --logger:"console;verbosity=normal"
