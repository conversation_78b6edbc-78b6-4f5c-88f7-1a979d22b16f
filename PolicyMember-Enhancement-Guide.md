# PolicyMember Enhancement Guide

## Overview

The `PolicyMember` entity has been enhanced following the QuoteMember pattern to provide comprehensive support for:

- **Temporal State Management** - Multiple states over time
- **Underwriting Management** - Status, loadings, exclusions
- **Pricing Information** - Premium calculations
- **Enhanced Dependency Management** - Better relationship tracking
- **Domain Logic and Invariants** - Business rules and validation

## Key Components

### 1. Core Value Objects

#### `UnderwritingStatus`
Represents the underwriting status of a policy member:
- `Pending` - Initial state, requires underwriting
- `Accepted` - Underwriting approved
- `Rejected` - Underwriting declined
- `Referred` - Requires manual review
- `RequiresReview` - Additional review needed

#### `PolicyMemberState`
Represents a temporal state with coverage details:
- `StartDate` / `EndDate` - Coverage period
- `PlanId` - Associated plan
- `Class` - Optional classification
- `Dependency` - Relationship to primary member
- `CustomFields` - Additional data

#### `PolicyMemberUnderwriting`
Comprehensive underwriting information:
- `Status` - Current underwriting status
- `Remark` - Underwriter notes
- `Loadings` - Additional charges
- `Exclusions` - Coverage exclusions
- `PreExistingConditions` - Medical conditions
- `NeedsUnderwriting` - Flag for review requirement

#### `PolicyMemberPricing`
Pricing information:
- `GrossPremium` / `NetPremium` - Base premiums
- `Tax` / `Discount` - Adjustments
- `Currency` - Currency code
- `CalculatedAt` - Calculation timestamp
- `TotalPremium` - Final premium amount

### 2. Enhanced PolicyMember Entity

#### State Management
```csharp
// Add a new coverage state
var state = PolicyMemberState.CreatePrimary(
    startDate: new DateOnly(2024, 1, 1),
    endDate: new DateOnly(2024, 12, 31),
    planId: "PLAN_001");
policyMember.AddState(state);

// Add dependent state
var dependency = PolicyMemberDependency.CreateSpouse(primaryMemberId);
var dependentState = PolicyMemberState.CreateDependent(
    startDate: new DateOnly(2024, 1, 1),
    endDate: new DateOnly(2024, 12, 31),
    planId: "PLAN_001",
    dependency: dependency);
policyMember.AddState(dependentState);

// Query active state
var currentState = policyMember.GetActiveStateOn(DateOnly.FromDateTime(DateTime.UtcNow));
```

#### Underwriting Management
```csharp
// Accept underwriting
policyMember.AcceptUnderwriting("Approved with standard terms");

// Add loading
var loading = PolicyMemberLoading.CreatePercentage("LOAD_001", "Age Loading", 25.0m, "Age over 50");
policyMember.AddLoading(loading);

// Add exclusion
var exclusion = PolicyMemberExclusion.CreatePermanent("EXC_001", "Pre-existing condition", "Diabetes");
policyMember.AddExclusion(exclusion);

// Reject underwriting
policyMember.RejectUnderwriting("High risk profile");
```

#### Pricing Management
```csharp
// Update pricing
var pricing = PolicyMemberPricing.Create(
    grossPremium: 1000.0m,
    netPremium: 950.0m,
    tax: 100.0m,
    discount: 50.0m,
    currency: "USD");
policyMember.UpdatePricing(pricing);

// Check if pricing can be calculated
if (policyMember.CanRunPricing)
{
    // Calculate and update pricing
}
```

### 3. Domain Events

The enhanced PolicyMember raises domain events for:
- `PolicyMemberStateAddedEvent` - When states are added
- `PolicyMemberStateRemovedEvent` - When states are removed
- `PolicyMemberUnderwritingStatusChangedEvent` - When underwriting status changes
- `PolicyMemberLoadingAddedEvent` / `PolicyMemberLoadingRemovedEvent` - Loading changes
- `PolicyMemberExclusionAddedEvent` / `PolicyMemberExclusionRemovedEvent` - Exclusion changes
- `PolicyMemberPricingUpdatedEvent` / `PolicyMemberPricingClearedEvent` - Pricing changes

### 4. Business Logic Properties

```csharp
// Check member status
bool isActive = policyMember.IsActive;
bool hasDependent = policyMember.HasDependents;
bool isDependent = policyMember.IsDependent;

// Get current information
PolicyMemberState? currentState = policyMember.CurrentState;
decimal? totalPremium = policyMember.TotalPremium;
```

## Usage Examples

### Creating a Primary Member with Coverage
```csharp
var policy = Policy.Create("POL001", startDate, endDate);
var primaryMember = policy.AddPolicyMember(
    memberId: "EMP001",
    startDate: new DateOnly(2024, 1, 1),
    endDate: new DateOnly(2024, 12, 31),
    planId: "HEALTH_BASIC");

// Accept underwriting
primaryMember.AcceptUnderwriting("Standard terms");

// Set pricing
var pricing = PolicyMemberPricing.Create(1200.0m, 1100.0m, 120.0m, 0.0m, "USD");
primaryMember.UpdatePricing(pricing);
```

### Adding a Dependent Member
```csharp
var spouseMember = policy.AddPolicyMember(
    memberId: "SPO001",
    startDate: new DateOnly(2024, 1, 1),
    endDate: new DateOnly(2024, 12, 31),
    planId: "HEALTH_BASIC",
    dependentOfPolicyMemberId: primaryMember.Id);

// The dependency relationship is automatically created
```

### Managing Underwriting with Loadings
```csharp
// Start with pending underwriting
var member = policy.AddPolicyMember("EMP002", startDate, endDate, "HEALTH_PREMIUM");

// Add pre-existing condition loading
var loading = PolicyMemberLoading.CreatePercentage("AGE_LOAD", "Age Loading", 15.0m);
member.AddLoading(loading);

// Accept with conditions
member.AcceptUnderwriting("Accepted with age loading");
```

## Migration Considerations

### Backward Compatibility
The enhanced PolicyMember maintains backward compatibility with existing properties:
- `StartDate`, `EndDate`, `PlanId` are automatically populated from the first state
- `DependentOfPolicyMemberId` is populated from state dependencies
- Existing `Fields` property continues to work

### Data Migration
For existing PolicyMembers, you may need to:
1. Create initial `PolicyMemberState` from existing date/plan data
2. Set default underwriting status to `Pending`
3. Migrate any existing pricing data to `PolicyMemberPricing`

## Best Practices

1. **State Management**
   - Always validate state dates don't overlap
   - Use meaningful plan IDs
   - Include custom fields for product-specific data

2. **Underwriting**
   - Set clear remarks for decisions
   - Use standard loading/exclusion codes
   - Track pre-existing conditions properly

3. **Pricing**
   - Validate pricing before setting
   - Clear pricing when underwriting is rejected
   - Use consistent currency codes

4. **Dependencies**
   - Validate dependency relationships
   - Avoid circular dependencies
   - Use standard relationship types

## Testing

The enhanced PolicyMember includes comprehensive validation and business logic that should be thoroughly tested:

1. **State Validation Tests**
   - Overlapping states
   - Invalid date ranges
   - Dependency validation

2. **Underwriting Workflow Tests**
   - Status transitions
   - Loading/exclusion management
   - Pricing interactions

3. **Domain Event Tests**
   - Event raising on state changes
   - Event data validation

This enhancement provides a robust foundation for complex insurance policy member management while maintaining compatibility with existing code.
