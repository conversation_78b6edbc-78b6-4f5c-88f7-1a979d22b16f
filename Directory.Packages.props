<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <!-- CoverGo -->
  <ItemGroup>
    <PackageVersion Include="CoverGo.BuildingBlocks.Api.GraphQl" Version="3.2.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Auth" Version="2.0.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Bootstrapper" Version="2.3.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.DataAccess" Version="9.0.4" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Domain.Core" Version="10.1.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Observability" Version="2.0.2" />
    <PackageVersion Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageVersion Include="HotChocolate.AspNetCore" Version="15.1.3" />
    <PackageVersion Include="Humanizer.Core" Version="2.14.1" />
    <PackageVersion Include="JunitXml.TestLogger" Version="6.1.0" />
    <PackageVersion Include="Mapster" Version="7.4.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.5.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.3" />
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />
  </ItemGroup>
</Project>